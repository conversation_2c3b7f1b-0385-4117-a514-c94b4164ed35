"use client";

import { LoadingScreen } from "@/components/_common/loading-screen";
import { tokenManager } from "@/lib/next-auth/local-token-manager";
import { useSession } from "next-auth/react";
import { ReactNode, useEffect } from "react";

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const session = useSession();

  useEffect(() => {
    if (session.status === "loading") return;

    if (session.status === "unauthenticated") {
      tokenManager.setAccessToken(null);
      return;
    }

    tokenManager.setAccessToken(session.data!.user.access_token);
  }, [session.status]);

  if (session.status === "loading") return <LoadingScreen />;

  return children;
}
