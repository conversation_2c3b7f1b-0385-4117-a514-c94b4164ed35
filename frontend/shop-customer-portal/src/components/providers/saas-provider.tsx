"use client";

import { ReactNode, useEffect } from "react";
import { CartDropdown } from "@/components/_common/header/cart-dropdown";
import { ProfileDropdown } from "@/components/_common/header/profile-dropdown";
import { SidebarTrigger } from "@/components/_common/header/sidebar-trigger";
import { LoadingScreen } from "@/components/_common/loading-screen";
import { setCartCookie } from "@/hooks/use-shopping-cart";
import { usePathname, useRouter } from "@/i18n/navigation";
import { getShoppingCartByEmail } from "@/lib/api/shoppingCart";
import { useCustomer } from "@/hooks/use-customer";
import { useQuery } from "@tanstack/react-query";

interface SaasProviderProps {
  children: ReactNode;
}

export function SaasProvider({ children }: SaasProviderProps) {
  const pathname = usePathname();
  const router = useRouter();

  const { customer, isLoading: isLoadingCustomer } = useCustomer();

  useEffect(() => {
    (async () => {
      if (isLoadingCustomer) return;

      if (!customer) return router.push("/auth/login");

      if (!customer.contracts.length) return router.push("/redirect");

      const contracts = customer.contracts;
      let errorPage = false;

      if (pathname === "/saas") {
        const euLicenseContract = contracts.find((contract) => contract.type === "EU_LICENSE");

        if (euLicenseContract) return router.push("/saas/eu-license");

        const directLicenseContract = contracts.find((contract) => contract.type === "DIRECT_LICENSE");

        if (directLicenseContract) return router.push("/saas/direct-license");

        const actionGuideContract = contracts.find((contract) => contract.type === "ACTION_GUIDE");

        if (actionGuideContract) return router.push("/saas/action-guide");
      }

      const isEuLicense = pathname.includes("eu-license");
      if (isEuLicense && !contracts.find((contract) => contract.type === "EU_LICENSE")) errorPage = true;

      const isDirectLicense = pathname.includes("direct-license");
      if (isDirectLicense && !contracts.find((contract) => contract.type === "DIRECT_LICENSE")) errorPage = true;

      const isActionGuide = pathname.includes("action-guide");
      if (isActionGuide && !contracts.find((contract) => contract.type === "ACTION_GUIDE")) errorPage = true;

      if (errorPage) {
        const firstContract = contracts[0];

        if (firstContract.type === "EU_LICENSE") return router.push("/saas/eu-license");

        if (firstContract.type === "DIRECT_LICENSE") return router.push("/saas/direct-license");

        if (firstContract.type === "ACTION_GUIDE") return router.push("/saas/action-guide");
      }
    })();
  }, [customer, pathname]);

  if (!customer || !customer.contracts.length) return <LoadingScreen />;

  return children;
}

export function SaasHeaderContent() {
  const { customer } = useCustomer();

  const shoppingCartQuery = useQuery({
    queryKey: ["shopping-cart"],
    queryFn: async () => {
      try {
        if (!customer?.email) return null;

        const customerCart = await getShoppingCartByEmail(customer?.email);

        if (!customerCart) return null;

        setCartCookie(customerCart.id);

        return customerCart;
      } catch {
        return null;
      }
    },
    enabled: customer?.email !== undefined,
  });

  return (
    <>
      <CartDropdown shoppingCart={shoppingCartQuery.data || null} />
      <ProfileDropdown />
      <SidebarTrigger />
    </>
  );
}
