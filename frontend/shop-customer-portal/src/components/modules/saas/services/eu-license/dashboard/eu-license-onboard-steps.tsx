import { Step } from "react-joyride";
import { ServiceOnboardStep } from "@/components/modules/saas/services/components/service-onboard/service-onboard-step";

export const euLicenseOnboardSteps: Step[] = [
  {
    content: "first",
    placement: "center",
    target: "body",
  },
  {
    content: (
      <ServiceOnboardStep
        title="Upload the required documents and information"
        description="After purchasing your license you must upload the documentation needed for us to be able to start the process!"
      />
    ),
    placement: "right-start",
    target: "#sb-eu-required-information",
    isFixed: true,
  },
  {
    content: (
      <ServiceOnboardStep
        title="Declare your volumes"
        description="Next step after uploading your documents is reporting the quantities you had sold for each country."
        videoUrl
      />
    ),
    placement: "right-start",
    target: "#sb-eu-declare-volumes",
    isFixed: true,
  },
  {
    content: <ServiceOnboardStep title="Quick access to your invoices" description="Keep track of your payments." />,
    placement: "right-start",
    placementBeacon: "top",
    target: "#sb-eu-third-party-invoices",
    isFixed: true,
  },
  {
    content: (
      <ServiceOnboardStep
        title="Check your contract information"
        description="Easy way to access all of your contract information per country"
      />
    ),
    placement: "right-start",
    placementBeacon: "top",
    target: "#sb-eu-contract-management",
    isFixed: true,
  },
  {
    content: (
      <ServiceOnboardStep
        title="Access your invoices and track payment status"
        description="Here you can check all payment related content"
      />
    ),
    placement: "right-start",
    placementBeacon: "top",
    target: "#sb-eu-invoices-payment",
    isFixed: true,
  },
  {
    content: (
      <ServiceOnboardStep
        title="Check your purchase licensed"
        description="You can always check the status of your licenses in this interactive map!"
        videoUrl
      />
    ),
    placement: "right-start",
    placementBeacon: "left",
    target: "#eu-dashboard-licenses",
    isFixed: true,
  },
  {
    content: <ServiceOnboardStep title="Visualize your task!" description="Keep track of your countries" />,
    placement: "left-start",
    target: "#dashboard-Map",
    title: "Our projects",
  },
  {
    content: (
      <ServiceOnboardStep
        title="Make sure to always stay up to date!"
        description="Here you can track the changes for the countries you had purchased."
        videoUrl
      />
    ),
    placement: "left-start",
    target: "#dashboard-law-changes",
    title: "Our Mission",
  },
  {
    content: "last",
    placement: "center",
    target: "body",
  },
];
