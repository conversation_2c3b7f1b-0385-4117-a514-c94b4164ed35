"use client";
import { SelectDropdown } from "@/components/_common/select-dropdown";
import { Skeleton } from "@/components/ui/skeleton";
import { usePricing } from "@/hooks/use-pricing";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { License } from "@/lib/api/license/types";
import { getPackagingServices } from "@/lib/api/packaging-services";
import { cn } from "@/lib/utils";
import { useCustomer } from "@/hooks/use-customer";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { useQuery } from "@tanstack/react-query";

import { DirectLicenseReportingTable } from "../components/direct-license-reporting-table";
import { DirectLicenseDeclareVolumesForm } from "./declare-volumes-form";
import { ReportTypeInformation } from "./report-type-information";

export const DIRECT_LICENSE_REPORT_TYPES = {
  VOLUME_CHANGE: "Volume change",
  VOLUME_XML: "Volume reporting central agency packaging register XML",
  INITIAL_PLANNED: "Initial planned volume report",
  END_OF_YEAR: "End-of-year volume report",
};

export type DirectLicenseReportType = keyof typeof DIRECT_LICENSE_REPORT_TYPES;

function getReportingType(licenses: License[], licenseYear: number): DirectLicenseReportType {
  const currentYear = new Date().getFullYear().toString();

  const isFutureYear = Number(licenseYear) > Number(currentYear);

  if (isFutureYear) {
    const hasFutureLicense = licenses.find((license) => license.year === Number(licenseYear));

    if (hasFutureLicense) return "VOLUME_CHANGE";

    return "INITIAL_PLANNED";
  }

  const isPastYear = Number(licenseYear) < Number(currentYear);

  if (isPastYear) return "END_OF_YEAR";

  return "VOLUME_CHANGE";
}

export function DirectLicenseVolumes() {
  const currentYear = new Date().getFullYear().toString();

  const { customer } = useCustomer();

  const { paramValues, changeParams, deleteParam } = useQueryFilter(["year", "xml"]);

  const licenseYear = paramValues?.year || currentYear;
  const isXmlSelected = paramValues?.xml === "true";

  const { priceLists, isLoading: isLoadingPriceLists } = usePricing({
    serviceType: "DIRECT_LICENSE",
  });

  const directLicenseContract = customer?.contracts.find((contract) => contract.type === "DIRECT_LICENSE");

  const directLicense = directLicenseContract?.licenses.find((license) => license.year === Number(licenseYear));

  const formattedPriceLists = (priceLists || [])
    .filter((p) => {
      if (p.condition_type_value >= currentYear) return true;

      const directLicense = directLicenseContract?.licenses.find(
        (license) => license.year === Number(p.condition_type_value)
      );

      return !!directLicense;
    })
    .sort((a, b) => Number(a.condition_type_value) - Number(b.condition_type_value));

  const reportType = getReportingType(directLicenseContract?.licenses || [], Number(licenseYear));

  const { data: packagingService, isLoading: isLoadingPackagingService } = useQuery({
    queryKey: ["packaging-services", { license_id: directLicense?.id }],
    queryFn: async () => {
      if (!directLicense) return null;

      const packagingServices = await getPackagingServices({
        license_id: directLicense.id,
      });

      return packagingServices[0] || null;
    },
    enabled: !!directLicense,
  });

  function handleOpenXml() {
    changeParams({ xml: "true" });
  }

  function handleCloseXml() {
    deleteParam("xml");
  }

  function handleChangeYear(year: string) {
    changeParams({ year, type: undefined, xml: undefined });
  }

  return (
    <div className="">
      <div>
        <div className="hidden md:block my-10">
          <p className="text-grey-blue font-bold mb-4">Select a year to report</p>
          <div className="flex items-center gap-5">
            {!isLoadingPriceLists &&
              formattedPriceLists.map((priceList) => (
                <Button
                  color="dark-blue"
                  variant="filled"
                  size="small"
                  onClick={() => handleChangeYear(priceList.condition_type_value)}
                  key={priceList.condition_type_value}
                  className={cn(
                    "rounded-2xl hover:text-white",
                    `${priceList.condition_type_value !== licenseYear && "bg-tonal-dark-blue-96 text-primary"}`
                  )}
                >
                  {priceList.condition_type_value}
                </Button>
              ))}
            {isLoadingPriceLists && (
              <>
                <Skeleton className="w-20 h-10 rounded-2xl" />
                <Skeleton className="w-20 h-10 rounded-2xl" />
                <Skeleton className="w-20 h-10 rounded-2xl" />
              </>
            )}
          </div>
        </div>
        <div className="flex items-center gap-2 md:hidden my-10">
          <p className="text-grey-blue font-bold text-nowrap">Select a year:</p>
          <SelectDropdown
            options={formattedPriceLists.map((priceList) => ({
              label: String(priceList.condition_type_value),
              value: String(priceList.condition_type_value),
            }))}
            value={String(licenseYear)}
            onChangeValue={(value) => handleChangeYear(value)}
          />
        </div>
      </div>
      <div className="space-y-8">
        <div className="flex flex-col md:flex-row md:items-center gap-5">
          <p className="text-primary text-base">Reporting Type:</p>
          <div className="flex flex-col md:flex-row md:items-center gap-6">
            <Button
              color="dark-blue"
              variant="filled"
              size="small"
              onClick={() => handleCloseXml()}
              data-selected={isXmlSelected}
              className="w-full md:w-auto rounded-2xl hover:text-white data-[selected=true]:bg-tonal-dark-blue-96 data-[selected=true]:text-primary"
            >
              {DIRECT_LICENSE_REPORT_TYPES[reportType]}
            </Button>
            {reportType !== "INITIAL_PLANNED" && (
              <>
                <div className="hidden md:block h-10 w-[1px] bg-tonal-dark-cream-80" />
                <Button
                  color="dark-blue"
                  variant="filled"
                  size="small"
                  onClick={() => handleOpenXml()}
                  data-selected={!isXmlSelected}
                  className="w-full md:w-auto rounded-2xl hover:text-white data-[selected=true]:bg-tonal-dark-blue-96 data-[selected=true]:text-primary"
                >
                  Volume reporting central agency packaging register XML
                </Button>
              </>
            )}
          </div>
        </div>
        {reportType !== "INITIAL_PLANNED" && (
          <p className="block md:hidden text-sm text-tonal-dark-cream-40">
            Please note: that we do not issue credit notes if you reduce your packaging volumes aftes 01.09.
            {licenseYear}
          </p>
        )}
        <ReportTypeInformation licenseYear={Number(licenseYear)} reportType={reportType} />
        {isXmlSelected && (
          <div className="mt-12">
            <p className="text-tonal-dark-cream-40 text-base">
              To comply with the packaging law it is necessary to transfer your currently licensed packaging volumes to
              the database “LUCID” of the Cental Packaging Register (ZSVR).
            </p>
            <br />
            <p className="text-tonal-dark-cream-40 text-base">
              According to legislation, we are unfortunately not allowed to do this for you. But if you can simply
              download the volume report by clicking the button bellow and then upload the file at the{" "}
              <a href="" className="text-support-blue underline">
                Producert data report / XML
              </a>{" "}
              upload on LUCID plataform.
            </p>
            <div className="mt-10">
              <DirectLicenseReportingTable licenseYear={Number(licenseYear)} />
            </div>
          </div>
        )}
        {!isXmlSelected && (
          <DirectLicenseDeclareVolumesForm
            reportType={reportType}
            licenseYear={Number(licenseYear)}
            packagingService={packagingService}
          />
        )}
      </div>
    </div>
  );
}
