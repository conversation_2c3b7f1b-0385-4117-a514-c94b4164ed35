import { Skeleton } from "@/components/ui/skeleton";
import { getPackagingServices } from "@/lib/api/packaging-services";
import { VolumeReport } from "@/lib/api/volume-report/types";
import { cn } from "@/lib/utils";
import { GERMANY_FRACTIONS } from "@/utils/consts/direct-license";
import { useCustomer } from "@/hooks/use-customer";
import { exportLucidXML } from "@/utils/exportLucidFile";
import { formatWeight } from "@/utils/format-weight";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Download } from "@arthursenno/lizenzero-ui-react/Icon";
import { useQuery } from "@tanstack/react-query";
import { enqueueSnackbar } from "notistack";
import { ComponentProps } from "react";

interface DirectLicenseReportingTableProps extends ComponentProps<"div"> {
  licenseYear: number;
}

export function DirectLicenseReportingTable({ licenseYear, className, ...props }: DirectLicenseReportingTableProps) {
  const { customer } = useCustomer();

  const directLicenseContract = customer?.contracts.find((c) => c.type === "DIRECT_LICENSE");

  const licenses = directLicenseContract?.licenses || [];

  const license = licenses.find((l) => l.year === licenseYear);

  const { data: volumeReport, isLoading: isLoadingVolumeReport } = useQuery<VolumeReport | null, Error>({
    queryKey: ["license-volumes", { license_id: license?.id }],
    queryFn: async () => {
      const packagingServices = await getPackagingServices({ license_id: Number(license?.id) });

      const packagingService = packagingServices[0];

      if (!packagingService) return null;

      const volumeReport = packagingService.volume_reports[0];

      if (!volumeReport) return null;

      return volumeReport;
    },
    enabled: !!license,
  });

  const fractions: { code: string; name: string; value: number }[] = (() => {
    return GERMANY_FRACTIONS.map((fraction) => ({
      code: fraction.code,
      name: fraction.name,
      value:
        volumeReport?.volume_report_items.find((item) => String(item.setup_fraction_code) === fraction.code)?.value ||
        0,
    }));
  })();

  const totalWeight = fractions.reduce((acc, fraction) => acc + fraction.value, 0);

  function handleDownloadVolumesXML() {
    if (!customer?.company?.lucid || !volumeReport) return;

    exportLucidXML(customer.company.lucid, volumeReport);

    enqueueSnackbar("Volumes XML exported successfully", { variant: "success" });
  }

  if (isLoadingVolumeReport || !volumeReport)
    return (
      <div className="space-y-5">
        <div className="p-4 space-y-4 bg-white rounded-md">
          <div className="pb-2 border-b border-tonal-dark-cream-80">
            <Skeleton className="h-6 w-72" />
          </div>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Skeleton className="h-5 w-32" />
              <Skeleton className="h-5 w-20" />
            </div>
            <div className="flex items-center gap-2">
              <Skeleton className="h-5 w-32" />
              <Skeleton className="h-5 w-20" />
            </div>
            <div className="flex items-center gap-2">
              <Skeleton className="h-5 w-32" />
              <Skeleton className="h-5 w-40" />
            </div>
            <div className="flex items-center gap-2">
              <Skeleton className="h-5 w-32" />
              <Skeleton className="h-5 w-24" />
            </div>
            <div className="flex items-center gap-2">
              <Skeleton className="h-5 w-32" />
              <Skeleton className="h-5 w-36" />
            </div>
          </div>
          <div>
            <div className="flex items-center justify-between border-b-2 border-tonal-dark-cream-80 p-1 pb-2">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-4 w-20" />
            </div>
            {[1, 2, 3, 4, 5].map((_, index) => (
              <div key={index} className="flex items-center justify-between p-1 border-b border-tonal-dark-cream-80">
                <Skeleton className="h-4 w-40" />
                <Skeleton className="h-4 w-24" />
              </div>
            ))}
          </div>
        </div>
        <div className="md:hidden">
          <Skeleton className="h-10 w-full" />
        </div>
        <div className="hidden md:block">
          <Skeleton className="h-12 w-80 ml-auto" />
        </div>
      </div>
    );

  return (
    <div className="space-y-5">
      <div className={cn("p-4 text-primary space-y-4 bg-white rounded-md", className)} {...props}>
        <p className="font-bold pb-2 border-b border-tonal-dark-cream-80">
          Volume Reporting for the licence year {licenseYear}
        </p>
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <p className="font-bold w-32 text-sm">License year</p>
            <p>{licenseYear}</p>
          </div>
          <div className="flex items-center gap-2">
            <p className="font-bold w-32 text-sm">Order number</p>
            <p>000000</p>
          </div>
          <div className="flex items-center gap-2">
            <p className="font-bold w-32 text-sm">Time and date</p>
            <p>{new Date(volumeReport.created_at).toLocaleString()}</p>
          </div>
          <div className="flex items-center gap-2">
            <p className="font-bold w-32 text-sm">Amount</p>
            <p>{formatWeight(totalWeight)}</p>
          </div>
          <div className="flex items-center gap-2">
            <p className="font-bold w-32 text-sm">Order</p>
            <p>License Contract {licenseYear}</p>
          </div>
        </div>
        <div>
          <div className="flex items-center justify-between border-b-2 border-tonal-dark-cream-80 p-1 pb-2">
            <p className="font-bold">Type of Material</p>
            <p className="font-bold">Volume</p>
          </div>
          {fractions.map((fraction, fractionIndex) => (
            <div
              key={fraction.code}
              data-last={fractionIndex === fractions.length - 1}
              className="flex items-center justify-between data-[last=false]:border-b data-[last=false]:border-tonal-dark-cream-80 p-1"
            >
              <p className="font-bold text-sm">{fraction.name}:</p>
              <p>{formatWeight(fraction.value)}</p>
            </div>
          ))}
        </div>
      </div>
      <Button
        onClick={handleDownloadVolumesXML}
        variant="filled"
        color="yellow"
        size="small"
        leadingIcon={<Download />}
        className="md:hidden w-full mt-6"
      >
        Download volumes for {licenseYear}
      </Button>
      <Button
        onClick={handleDownloadVolumesXML}
        variant="filled"
        color="yellow"
        size="medium"
        leadingIcon={<Download />}
        className="hidden md:flex ml-auto mt-8"
      >
        Download your current volumes for {licenseYear}
      </Button>
    </div>
  );
}
