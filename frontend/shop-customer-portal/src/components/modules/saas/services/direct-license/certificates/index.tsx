"use client";

import { CountryIcon } from "@/components/_common/country-icon";
import { Divider } from "@/components/_common/divider";
import {
  QuestionTooltip,
  QuestionTooltipDescription,
  QuestionTooltipTitle,
} from "@/components/_common/question-tooltip";
import { TitleAndSubTitle } from "@/components/_common/titleAndSubTitle";
import { TooltipIcon } from "@/components/_common/tooltipIcon";
import { SaasBreadcrumb } from "@/components/modules/saas/components/saas-breadcrumb";
import { SaasContainer } from "@/components/modules/saas/components/saas-container";
import { Certificate } from "@/lib/api/certificates/types";
import { downloadCustomerFile } from "@/lib/api/file";
import { getLicenses } from "@/lib/api/license";
import { useCustomer } from "@/hooks/use-customer";
import { COUNTRIES } from "@/utils/consts/countries";
import { downloadFile } from "@/utils/download-file";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Download, Pdf } from "@arthursenno/lizenzero-ui-react/Icon";
import { useMutation, useQuery } from "@tanstack/react-query";
import { enqueueSnackbar } from "notistack";
import { CgSpinnerAlt } from "react-icons/cg";

const paths = [
  { label: "Dashboard DE", href: "../direct-license" },
  { label: "Certificates", href: "#" },
];

const country = COUNTRIES.find((country) => country.code === "DE")!;

export function DirectLicenseCertificates() {
  const { customer } = useCustomer();
  const directLicenseContract = customer?.contracts.find((contract) => contract.type === "DIRECT_LICENSE");

  const { data: licenses } = useQuery({
    queryKey: ["licenses", { contract_id: directLicenseContract?.id }],
    queryFn: async () => {
      if (!directLicenseContract) return [];

      return getLicenses({ contract_id: directLicenseContract.id });
    },
    enabled: !!directLicenseContract,
  });

  const certificates = licenses?.[0]?.certificates;

  const downloadCertificateMutation = useMutation({
    mutationFn: async (certificate: Certificate) => {
      const certificateFile = certificate.files?.[0];

      if (!certificateFile || !customer) throw new Error();

      const file = await downloadCustomerFile(certificateFile.id);

      downloadFile({ buffer: file, fileName: certificateFile.original_name });
    },
  });

  async function handleDownloadCertificate(certificate: Certificate) {
    downloadCertificateMutation.mutate(certificate, {
      onSuccess: () => {
        enqueueSnackbar("File downloaded successfully", { variant: "success" });
      },
      onError: () => {
        enqueueSnackbar("Error downloading file. Please try again.", { variant: "error" });
      },
    });
  }

  return (
    <>
      <SaasBreadcrumb paths={paths} />
      <SaasContainer>
        <TitleAndSubTitle showIcon title="Certificates" subText="Download the PDF file of your certificates" />
        <div className="w-full rounded-[32px] items-start bg-surface-02 flex flex-col px-4 py-6 md:py-7 md:px-8">
          <div className="flex flex-row gap-3 items-center">
            <CountryIcon country={country} className="size-6" />
            <p className="text-primary font-bold text-[28px]">Germany</p>
          </div>

          <Divider style={{ margin: "28px 0" }} />

          {certificates?.map((certificate) => (
            <div key={certificate.id} className="flex flex-row w-full justify-between">
              <div className="flex gap-2 items-center">
                <Pdf className="size-10" />
                <span className="text-on-tertiary font-bold">{certificate.name}</span>
                <QuestionTooltip>
                  <QuestionTooltipTitle className="text-primary font-bold">
                    Infos Participation Certificate
                  </QuestionTooltipTitle>
                  <QuestionTooltipDescription>
                    Lorem, ipsum dolor sit amet consectetur adipisicing elit. Exercitationem excepturi at eum, sint
                    odit, voluptatem repellat deleniti explicabo ad eligendi numquam itaque molestiae? Fugiat voluptates
                    officiis et dolores commodi harum!
                  </QuestionTooltipDescription>
                </QuestionTooltip>
              </div>
              <div className="flex items-center">
                <Button
                  color="dark-blue"
                  trailingIcon={
                    downloadCertificateMutation.isPending ? <CgSpinnerAlt className="animate-spin" /> : <Download />
                  }
                  disabled={!certificate.files.length || downloadCertificateMutation.isPending}
                  size="medium"
                  variant="text"
                  onClick={() => handleDownloadCertificate(certificate)}
                >
                  Download
                </Button>
                {!certificate.files.length && <TooltipIcon info="Certificate not ready yet to download" />}
              </div>
            </div>
          ))}

          <Divider style={{ margin: 0, marginTop: "14px" }} />
        </div>
      </SaasContainer>
    </>
  );
}
