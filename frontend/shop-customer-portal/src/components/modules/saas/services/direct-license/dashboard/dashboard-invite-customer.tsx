"use client";

import { QuestionTooltip, QuestionTooltipDescription } from "@/components/_common/question-tooltip";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { BiLinkExternal, BiCopy } from "react-icons/bi";
import { enqueueSnackbar } from "notistack";
import { FileCopy, Link } from "@arthursenno/lizenzero-ui-react/Icon";
import { useQuery } from "@tanstack/react-query";
import { getCustomerById } from "@/lib/api/customer";
import { useCustomer } from "@/hooks/use-customer";

import { useRouter } from "@/i18n/navigation";

const BASE_URL = typeof window !== "undefined" ? window.location.origin : "";

export function DashboardInviteCustomer() {
  const { customer } = useCustomer();
  const router = useRouter();

  const { data: customerData, isPending } = useQuery<{ coupon: string; link: string }>({
    queryKey: ["invite-customer", customer?.id],
    queryFn: async () => {
      const res = await getCustomerById(customer!.id!);

      const couponFind = res.coupons?.find((item: any) => item.coupon.type === "CUSTOMER");
      const coupon = couponFind?.coupon;
      const link = `${BASE_URL}/shop-invite?customer_id=${customer?.id}`;

      return {
        coupon: coupon?.code,
        link,
      };
    },
    enabled: !!customer?.company?.id,
  });

  const link = customerData?.link;
  const coupon = customerData?.coupon;

  const handleOpenLink = () => {
    if (!link) return;

    navigator.clipboard.writeText(link);
    enqueueSnackbar("Copied link!", { variant: "success" });
  };

  const handleCopyCoupon = () => {
    if (!coupon) return;

    navigator.clipboard.writeText(coupon);
    enqueueSnackbar("Copied coupon!", { variant: "success" });
  };

  return (
    <div
      id="invite-customer"
      className="col-span-1 md:col-span-11 rounded-[52px] flex flex-col md:flex-row items-center overflow-hidden"
    >
      <div className="order-2 md:order-1 w-full flex-1 pl-6 pr-5 pb-6 pt-32 md:p-14 bg-tonal-dark-blue-90 rounded-[52px] max-md:-mt-24 md:-mr-24 z-10 md:z-20">
        <div className="flex items-center gap-2">
          <h2 className="text-title-3 font-bold text-primary">Invite customer</h2>
          <QuestionTooltip>
            <QuestionTooltipDescription>
              Lorem ipsum dolor sit amet consectetur. Gravida vitae fermentum faucibus arcu non.
            </QuestionTooltipDescription>
          </QuestionTooltip>
        </div>
        <p className="text-small-paragraph-regular text-primary my-5">Marketing campaing lorem ipsum dolor sit amet.</p>
        <div className="pt-4 pb-6 space-y-4">
          <p className="text-paragraph-regular text-primary my-5">My discounts</p>
          <div className="space-y-6">
            {/* Link */}
            <div className="w-full flex items-center justify-between gap-4 py-4 pl-5 pr-4 rounded-full bg-background border-[1px] border-tonal-dark-cream-80">
              <p className="text-primary font-bold flex-1 overflow-hidden text-ellipsis text-nowrap">
                {isPending ? `Loading...` : link}
              </p>
              <Button
                disabled={isPending}
                variant="filled"
                color="yellow"
                size="iconXSmall"
                onClick={handleOpenLink}
                trailingIcon={<Link className="size-5" />}
              />
            </div>

            {/* Cupom */}
            <div className="w-full md:w-2/3 flex items-center justify-between gap-4 py-4 pl-5 pr-4 rounded-full bg-background border-[1px] border-tonal-dark-cream-80">
              <p className="text-primary font-bold flex-1">{isPending ? `Loading...` : coupon}</p>
              <Button
                disabled={isPending}
                variant="filled"
                color="yellow"
                size="iconXSmall"
                onClick={handleCopyCoupon}
                trailingIcon={<FileCopy className="size-5" />}
              />
            </div>
          </div>
        </div>
      </div>

      <div
        style={{ backgroundImage: "url('/assets/images/packaging_woman.png')" }}
        className="order-1 md:order-2 h-40 md:h-full w-full rounded-[40px] md:w-1/2 bg-tonal-dark-cream-60 z-20 md:z-10"
      ></div>
    </div>
  );
}
