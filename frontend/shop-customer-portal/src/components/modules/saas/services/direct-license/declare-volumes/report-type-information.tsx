"use client";

import Link from "next/link";
import { DirectLicenseReportType } from "./direct-license-volumes";
import { useState } from "react";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Error } from "@arthursenno/lizenzero-ui-react/Icon";

interface ReportTypeInformationProps {
  licenseYear: number;
  reportType: DirectLicenseReportType;
}

export function ReportTypeInformation({ licenseYear, reportType }: ReportTypeInformationProps) {
  const [isOpen, setIsOpen] = useState(false);
  const currentYear = new Date().getFullYear();

  if (reportType === "END_OF_YEAR") {
    return (
      <div className="space-y-4">
        {isOpen && (
          <>
            <p className="text-tonal-dark-cream-40 text-sm md:text-base">
              As an “initial distributor”, you must comply with German Packaging Act by reporting the volumes of
              packagign that you actually places on the market after the end od a licensing year (1 Jan to 31 Dec). This
              annual report must be sent to your dual system and to the{" "}
              <Link href="/saas/direct-license/lucid" className="text-support-blue underline">
                Packaging Register LUCID
              </Link>
            </p>
            <ul className="text-tonal-dark-cream-40 text-sm md:text-base list-item list-disc">
              <li>Please check yhe volumes you reported for last year, compare</li>
              <li>These to the actual packaging volumes you placed on the market</li>
              <li>Send your end-of-year volume report to the Central Agency and to us at Interseroh</li>
            </ul>
          </>
        )}
        <Button
          variant="text"
          color="light-blue"
          size="small"
          onClick={() => setIsOpen(!isOpen)}
          className="md:hidden"
          leadingIcon={<Error className="text-support-blue" />}
        >
          {isOpen ? "Hide information" : "Show information"}
        </Button>
      </div>
    );
  }

  if (reportType === "INITIAL_PLANNED") {
    return (
      <div className="space-y-4">
        {isOpen && (
          <>
            <p className="text-tonal-dark-cream-40 text-sm md:text-base">
              The initial planned volume report for {licenseYear} will be released in the 4th quarter. For volume
              reports regarding the current licence year {currentYear}, please use the “
              <a href="" className="text-support-blue underline">
                volume change
              </a>
              ” tab
            </p>
            <p className="text-tonal-dark-cream-40 text-sm md:text-base">
              To comply with the German Packaging Law it is necessary to transfer your currently licensed packaging
              volumes to the database “LUCID” of the Central Packaging Register (ZSVR). Please select “Interseroh +
              GmbH” as system operator.
            </p>
            <p className="text-tonal-dark-cream-40 text-sm md:text-base">
              According to legislation, we are unfortunately not allowed to do this for you. But with our{" "}
              <a href="" className="text-support-blue underline">
                Download-Service
              </a>
              we make it as easy as possible for you and save you the tedious work of manually entering your volumes ar
              LUCID.
            </p>
          </>
        )}
        <Button
          variant="text"
          color="light-blue"
          size="small"
          onClick={() => setIsOpen(!isOpen)}
          className="md:hidden"
          leadingIcon={<Error className="text-support-blue" />}
        >
          {isOpen ? "Hide information" : "Show information"}
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {isOpen && (
        <>
          <p className="text-tonal-dark-cream-40 text-sm md:text-base">
            Here you can make a quantity adjustment fo the current calendar year at any time.
          </p>
          <p className="text-tonal-dark-cream-40 text-sm md:text-base">
            After you have reported your quantities to us in this way, please also enter them in LUCID. Please make sure
            that the reported quantities ares the same for both Lizenzero and LUCID.{" "}
            <Link href="/saas/direct-license/lucid" className="text-support-blue underline">
              Mid-year Report for LUCID
            </Link>
          </p>
        </>
      )}
      <Button
        variant="text"
        color="light-blue"
        size="small"
        onClick={() => setIsOpen(!isOpen)}
        className="md:hidden"
        leadingIcon={<Error className="text-support-blue" />}
      >
        {isOpen ? "Hide information" : "Show information"}
      </Button>
    </div>
  );
}
