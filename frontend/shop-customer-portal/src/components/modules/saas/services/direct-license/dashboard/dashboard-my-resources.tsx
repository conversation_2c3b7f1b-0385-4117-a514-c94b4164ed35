"use client";

import { QuestionTooltip, QuestionTooltipDescription } from "@/components/_common/question-tooltip";
import { getPackagingService, getPackagingServices } from "@/lib/api/packaging-services";
import { useCustomer } from "@/hooks/use-customer";
import { GLASS_RESOURCES_FACTOR, PAPER_RESOURCES_FACTOR, PLASTICS_RESOURCES_FACTOR } from "@/utils/system-consts";
import { useQuery } from "@tanstack/react-query";
import Image from "next/image";
import { Link } from "@/i18n/navigation";
import { useCallback, useEffect, useState } from "react";
import { BiLinkExternal } from "react-icons/bi";

interface DashboardMyResourcesProps {}

export function DashboardMyResources({}: DashboardMyResourcesProps) {
  const [resourcesSaved, setResourcesSaved] = useState<number>(0);
  const [paperTotal, setPaperTotal] = useState<number>(0);
  const [plasticsTotal, setPlasticsTotal] = useState<number>(0);
  const [glassTotal, setGlassTotal] = useState<number>(0);

  const { customer } = useCustomer();

  const directLicenseContract = customer?.contracts.find((c) => c.type === "DIRECT_LICENSE");

  const license = directLicenseContract?.licenses.find((l) => l.country_code === "DE");

  const { data: packagingServices } = useQuery({
    queryKey: ["germany-packaging-services", { license_id: license?.id }],
    queryFn: async () => {
      return license ? await getPackagingServices({ license_id: license?.id }) : [];
    },
    enabled: !!license,
  });

  const calculateTotals = useCallback(() => {
    const newPaperTotal = 0;
    const newPlasticsTotal = 0;
    const newGlassTotal = 0;

    // options.forEach((form) => {
    //   // const value = formValues[form.id] || 0;
    //   const value = formValues[form.id] || 0;
    //   if (form.multipliers.paper) newPaperTotal += value * (form.multipliers.paper / 1000);
    //   if (form.multipliers.plastic) newPlasticsTotal += value * (form.multipliers.plastic / 1000);
    //   if (form.multipliers.glass) newGlassTotal += value * (form.multipliers.glass / 1000);
    // });

    setPaperTotal(newPaperTotal);
    setPlasticsTotal(newPlasticsTotal);
    setGlassTotal(newGlassTotal);
  }, []);

  const calculateResourcesSaved = useCallback(() => {
    if (!customer || !license) return;

    // const saved = 0;

    // Object.values({} as Record<string, Fraction>)
    //   .filter((fraction) => fraction.value)
    //   .forEach((fraction) => {
    //     const value = fraction.value || 0;
    //     const multipler =
    //       germanyPackaging.reportSet.items.find((item) => item.id == +fraction.key)?.resourcesMultiplier || 1;
    //     saved += value * multipler;
    //   });

    // setResourcesSaved(saved);

    const paperSavings = paperTotal * PAPER_RESOURCES_FACTOR;
    const plasticsSavings = plasticsTotal * PLASTICS_RESOURCES_FACTOR;
    const glassSavings = glassTotal * GLASS_RESOURCES_FACTOR;

    const totalSavings = paperSavings + plasticsSavings + glassSavings;
    setResourcesSaved(totalSavings);
  }, [customer, glassTotal, license, paperTotal, plasticsTotal]);

  useEffect(() => {
    calculateResourcesSaved();
  }, [calculateResourcesSaved]);

  if (!customer || !license) return null;

  return (
    <div
      id="my-resources"
      className="col-span-1 md:col-span-11 px-4 py-8 md:p-10 bg-background rounded-[40px] space-y-7"
    >
      <div className="flex items-center gap-2">
        <h2 className="text-title-3 font-bold text-primary">My resources</h2>
        <QuestionTooltip>
          <QuestionTooltipDescription>
            Lorem ipsum dolor sit amet consectetur. Gravida vitae fermentum faucibus arcu non.
          </QuestionTooltipDescription>
        </QuestionTooltip>
      </div>
      <div className="hidden md:flex gap-10 p-10 bg-tonal-green-90 rounded-3xl relative overflow-hidden">
        <svg
          className="absolute top-0 right-0 scale-110"
          width="425"
          height="264"
          viewBox="0 0 425 264"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M101.273 185.792C176.92 213.135 253.569 207.667 334.059 175.02C409.205 144.528 467.986 91.167 505.893 26.2056C493.535 17.5883 474.164 7.81092 440.265 2.17652C387.83 -6.60652 318.528 9.96525 270.936 74.4295C269.767 75.9209 268.765 77.4124 267.596 78.9039C264.089 83.7097 260.916 88.5155 257.743 93.6527C245.553 113.207 228.353 129.282 208.147 140.054C190.947 149.168 171.576 154.471 151.036 154.968C150.702 154.968 149.867 154.968 149.533 154.968C106.95 155.465 69.0433 133.425 45.6645 100.447C44.9965 99.6186 44.4955 98.79 43.8276 97.7957C38.9848 90.5041 34.643 82.7154 31.3032 74.4295C26.9614 63.9892 24.1226 52.8862 22.4527 41.4516C21.9517 38.1373 21.9517 38.1373 21.6177 37.3087C20.1148 32.1714 15.606 28.5256 10.0953 28.5256C8.42539 28.5256 6.92247 28.8571 5.58654 29.52C5.58654 29.52 5.58654 29.52 5.41955 29.52C1.57875 31.3429 -0.425146 35.6515 0.0758278 39.7944C4.25061 67.635 12.7672 93.9842 24.9575 118.179L29.2993 125.636C45.1635 152.648 69.7112 174.026 99.2687 184.798C99.4357 184.798 101.44 185.626 101.607 185.626L101.273 185.792Z"
            fill="#F0FAF0"
          />
          <path
            d="M519.918 36.9746C485.351 109.559 424.566 170.046 344.076 202.692C260.079 236.83 170.238 234.344 92.2529 202.692C137.842 240.476 196.456 263.345 260.413 263.345C294.145 263.345 326.375 257.048 355.932 245.448C357.268 244.95 358.437 244.453 359.773 243.956C376.806 236.996 393.839 227.384 409.87 216.778C415.381 213.133 420.725 209.155 425.902 205.012C426.236 204.847 426.403 204.515 426.737 204.35C427.572 203.687 428.24 203.024 429.074 202.361C437.424 195.401 445.273 187.944 452.787 179.989C468.985 162.589 482.846 143.034 493.867 122.485C493.867 122.485 494.034 122.154 494.201 121.988C494.702 121.159 495.036 120.331 495.537 119.502C508.395 94.4787 517.246 66.9695 521.254 38.1346C520.753 37.8032 520.419 37.4718 519.918 37.1403V36.9746Z"
            fill="#F0FAF0"
          />
        </svg>
        <div className="z-10 flex-none">
          <svg width="284" height="284" viewBox="0 0 284 284" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M156.184 4.21935C162.129 8.11613 169.446 9.39355 176.385 7.74194C186.42 5.35484 196.912 9.12258 203.041 17.329C207.274 22.9935 213.716 26.671 220.811 27.4581C231.068 28.6065 239.627 35.6903 242.541 45.471C244.566 52.2323 249.335 57.8452 255.725 60.9935C264.963 65.5355 270.556 75.0839 269.915 85.2516C269.471 92.2839 272.019 99.1742 276.932 104.284C284.04 111.665 285.987 122.529 281.858 131.871C279.01 138.335 279.01 145.665 281.858 152.129C285.987 161.471 284.04 172.335 276.932 179.716C272.019 184.826 269.471 191.716 269.915 198.748C270.556 208.916 264.963 218.477 255.725 223.006C249.335 226.142 244.553 231.768 242.541 238.529C239.627 248.297 231.055 255.394 220.811 256.542C213.716 257.329 207.287 261.006 203.041 266.671C196.912 274.865 186.407 278.645 176.385 276.258C169.446 274.606 162.129 275.884 156.184 279.781C147.586 285.406 136.414 285.406 127.816 279.781C121.871 275.884 114.554 274.606 107.615 276.258C97.5801 278.645 87.0877 274.877 80.9594 266.671C76.7259 261.006 70.284 257.329 63.1889 256.542C52.9316 255.394 44.373 248.31 41.4592 238.529C39.4338 231.768 34.6645 226.155 28.275 223.006C19.0369 218.465 13.4444 208.916 14.0847 198.748C14.5289 191.716 11.9809 184.826 7.06791 179.716C-0.0403078 172.335 -1.98723 161.471 2.14181 152.129C4.99033 145.665 4.99033 138.335 2.14181 131.871C-1.98723 122.529 -0.0403078 111.665 7.06791 104.284C11.9809 99.1742 14.5289 92.2839 14.0847 85.2516C13.4444 75.0839 19.0369 65.5226 28.275 60.9935C34.6645 57.8581 39.4469 52.2323 41.4592 45.471C44.373 35.7032 52.9447 28.6065 63.1889 27.4581C70.284 26.671 76.7128 22.9935 80.9594 17.329C87.0877 9.13548 97.5932 5.35484 107.615 7.74194C114.554 9.39355 121.871 8.11613 127.816 4.21935C136.414 -1.40645 147.586 -1.40645 156.184 4.21935Z"
              fill="#1B6C64"
            />
            <g clip-path="url(#clip0_4128_82138)">
              <path
                d="M105.352 182.493C121.79 188.435 138.446 187.247 155.937 180.153C172.266 173.527 185.04 161.931 193.277 147.815C190.592 145.942 186.382 143.817 179.016 142.593C167.622 140.684 152.562 144.286 142.22 158.294C141.966 158.618 141.748 158.942 141.494 159.266C140.732 160.311 140.043 161.355 139.353 162.471C136.704 166.721 132.967 170.214 128.576 172.554C124.838 174.535 120.629 175.687 116.165 175.795C116.093 175.795 115.911 175.795 115.839 175.795C106.585 175.903 98.3481 171.114 93.2678 163.948C93.1226 163.768 93.0138 163.588 92.8686 163.371C91.8163 161.787 90.8728 160.094 90.147 158.294C89.2035 156.025 88.5867 153.612 88.2238 151.128C88.1149 150.407 88.1149 150.407 88.0423 150.227C87.7157 149.111 86.736 148.319 85.5385 148.319C85.1756 148.319 84.849 148.391 84.5587 148.535C84.5587 148.535 84.5587 148.535 84.5224 148.535C83.6878 148.931 83.2523 149.867 83.3612 150.768C84.2684 156.817 86.1191 162.543 88.7681 167.801L89.7116 169.421C93.1589 175.291 98.4932 179.937 104.916 182.277C104.952 182.277 105.388 182.457 105.424 182.457L105.352 182.493Z"
                fill="#D8F2D8"
              />
              <path
                d="M196.325 150.154C188.813 165.927 175.604 179.071 158.114 186.165C139.861 193.584 120.338 193.044 103.392 186.165C113.298 194.376 126.035 199.345 139.933 199.345C147.264 199.345 154.267 197.977 160.69 195.456C160.98 195.348 161.234 195.24 161.525 195.132C165.226 193.62 168.927 191.531 172.411 189.226C173.609 188.434 174.77 187.57 175.895 186.67C175.967 186.634 176.004 186.562 176.076 186.526C176.258 186.381 176.403 186.237 176.584 186.093C178.399 184.581 180.104 182.96 181.737 181.232C185.257 177.451 188.269 173.201 190.664 168.736C190.664 168.736 190.7 168.664 190.736 168.628C190.845 168.448 190.918 168.268 191.027 168.088C193.821 162.65 195.744 156.672 196.615 150.406C196.506 150.334 196.434 150.262 196.325 150.19V150.154Z"
                fill="#D8F2D8"
              />
              <path
                d="M174.515 102.873C158.077 96.9309 141.421 98.1193 123.93 105.213C107.637 111.876 94.827 123.471 86.626 137.551C89.3476 139.424 93.5207 141.549 100.887 142.773C112.281 144.682 127.341 141.081 137.683 127.072C137.937 126.748 138.155 126.424 138.409 126.1C139.171 125.056 139.86 124.011 140.55 122.895C143.199 118.646 146.936 115.153 151.327 112.812C155.065 110.831 159.274 109.679 163.738 109.571C163.81 109.571 163.992 109.571 164.064 109.571C173.318 109.463 181.555 114.252 186.635 121.418C186.78 121.599 186.889 121.779 187.034 121.995C188.087 123.579 189.03 125.272 189.756 127.072C190.699 129.341 191.316 131.754 191.679 134.238C191.788 134.959 191.788 134.959 191.861 135.139C192.187 136.255 193.167 137.047 194.365 137.047C194.727 137.047 195.054 136.975 195.344 136.831C195.344 136.831 195.344 136.831 195.381 136.831C196.215 136.435 196.651 135.499 196.542 134.599C195.635 128.549 193.784 122.823 191.135 117.565L190.191 115.945C186.744 110.075 181.41 105.43 174.987 103.089C174.951 103.089 174.515 102.909 174.479 102.873H174.515Z"
                fill="#D8F2D8"
              />
              <path
                d="M121.789 99.1996C140.042 91.7813 159.565 92.3215 176.512 99.1996C166.605 90.9891 153.868 86.0195 139.97 86.0195C132.64 86.0195 125.636 87.388 119.213 89.9087C118.923 90.0168 118.669 90.1248 118.378 90.2328C114.677 91.7453 110.976 93.8339 107.492 96.1386C106.295 96.9309 105.133 97.7952 104.008 98.6954C103.936 98.7314 103.863 98.8035 103.827 98.8395C103.646 98.9835 103.5 99.1276 103.319 99.2716C101.505 100.784 99.7991 102.405 98.1661 104.133C94.6099 107.914 91.6343 112.164 89.2393 116.629C89.2393 116.629 89.2393 116.701 89.1667 116.737C89.0579 116.917 88.9853 117.097 88.8764 117.277C86.0822 122.715 84.159 128.693 83.2881 134.959C83.3969 135.031 83.4695 135.103 83.5784 135.175C91.09 119.438 104.299 106.294 121.789 99.1996Z"
                fill="#D8F2D8"
              />
            </g>
            <path
              d="M36.8785 153.972C36.7309 152.677 37.6734 151.496 38.9452 151.349C40.2171 151.201 41.3981 152.098 41.5457 153.45C41.6933 154.801 40.7735 155.925 39.479 156.073C38.2071 156.22 37.0261 155.301 36.8785 153.972Z"
              fill="#D8F2D8"
            />
            <path
              d="M45.6795 139.539L45.4864 143.513L26.8291 142.593L27.1357 136.382C27.3401 132.362 29.6339 129.659 33.4494 129.852C36.118 129.989 37.9009 131.68 38.639 133.952L46.2019 129.171L45.9634 133.884L38.9115 138.131L38.8547 139.198L45.6795 139.539ZM35.5502 139.039L35.6638 136.768C35.7433 135.167 34.8916 133.952 33.3245 133.872C31.7234 133.793 30.8149 134.928 30.7354 136.518L30.6219 138.778L35.5502 139.028V139.039Z"
              fill="#D8F2D8"
            />
            <path
              d="M45.5659 116.771C47.4851 118.259 48.3822 120.609 47.8371 123.437C47.0535 127.468 43.7036 129.762 39.6951 128.979C35.7887 128.218 33.4381 124.777 34.1876 120.95C34.9484 117.044 38.3324 114.898 42.3864 115.692C42.8292 115.783 43.1472 115.863 43.3175 115.931L41.4666 125.447C43.0109 125.606 44.385 124.436 44.6916 122.835C44.9755 121.393 44.5099 120.541 43.6582 119.758L45.5659 116.783V116.771ZM40.1039 119.133C38.6844 119.042 37.4694 119.985 37.1628 121.552C36.8675 123.074 37.776 124.55 38.9797 124.925L40.1039 119.133Z"
              fill="#D8F2D8"
            />
            <path
              d="M38.3444 105.631C38.7078 104.541 39.3437 103.485 40.1727 102.758L43.6362 103.916C42.7391 104.575 42.1372 105.381 41.8193 106.323C41.172 108.277 42.0237 110.162 43.9314 110.798C45.8278 111.434 47.6561 110.434 48.3033 108.481C48.6327 107.493 48.5759 106.403 48.2579 105.426L51.7555 106.596C52.0053 107.8 51.8577 108.992 51.4943 110.082C50.2111 113.932 46.6227 115.828 42.6936 114.511C38.9236 113.25 37.0385 109.549 38.3444 105.631Z"
              fill="#D8F2D8"
            />
            <path
              d="M57.4553 108.277L54.5255 106.857C54.9116 106.539 55.2182 106.153 55.4681 105.653C55.9677 104.62 55.8996 103.723 54.9798 102.69L54.3779 101.986L40.2061 101.032L42.0002 97.3411L51.38 98.2496L44.8959 91.3908L46.6447 87.791L56.9556 99.3624C59.4198 102.11 59.9308 104.279 58.852 106.516C58.3978 107.448 57.8641 107.993 57.4553 108.299V108.277Z"
              fill="#D8F2D8"
            />
            <path
              d="M50.7667 80.966C51.3913 80.0007 52.277 79.1491 53.265 78.6494L56.331 80.6367C55.2976 81.0455 54.5027 81.6814 53.969 82.5103C52.8562 84.2364 53.2082 86.2804 54.8888 87.3706C56.5695 88.4607 58.5794 87.9611 59.7036 86.235C60.2714 85.3606 60.4872 84.2932 60.419 83.2712L63.5078 85.2811C63.4396 86.5075 62.9968 87.6204 62.3722 88.5856C60.1579 91.981 56.2175 92.9235 52.7426 90.6637C49.4041 88.4948 48.507 84.4408 50.7667 80.9773V80.966Z"
              fill="#D8F2D8"
            />
            <path d="M52.549 69.8828L67.2546 81.4088L64.8472 84.4748L50.1416 72.9488L52.549 69.8828Z" fill="#D8F2D8" />
            <path
              d="M74.7497 68.668C75.5446 70.9619 75.0223 73.4147 73.0237 75.4928C70.1734 78.4453 66.1194 78.5361 63.1783 75.7085C60.3167 72.9491 60.2258 68.7816 62.9285 65.9768C65.6879 63.1151 69.6965 63.1605 72.6716 66.0335C73.0009 66.3515 73.2054 66.59 73.3303 66.7376L66.6077 73.7099C67.8114 74.6865 69.6056 74.4481 70.7298 73.2784C71.7518 72.2224 71.8313 71.2571 71.5474 70.1329L74.7724 68.6794L74.7497 68.668ZM68.8788 67.6801C67.7319 66.8284 66.1989 66.9533 65.0974 68.1116C64.0186 69.2245 63.9732 70.9619 64.7908 71.9271L68.8902 67.6801H68.8788Z"
              fill="#D8F2D8"
            />
            <path
              d="M77.998 69.9395L66.665 55.0977L69.7424 52.7471L74.1484 58.5157C74.0689 56.8692 74.8979 55.3248 76.2833 54.2687C79.0768 52.1339 83.0626 52.8493 85.4359 55.9721C87.7979 59.0608 87.4686 63.1148 84.6751 65.2497C83.3011 66.3057 81.6318 66.76 80.0534 66.2262L81.0867 67.5776L78.0093 69.9282L77.998 69.9395ZM77.5892 57.6641C76.0789 58.811 75.8177 60.9345 76.976 62.4334C78.1229 63.9324 80.235 64.239 81.7453 63.0921C83.2556 61.9452 83.5168 59.8216 82.3699 58.3227C81.223 56.8124 79.1108 56.5171 77.6005 57.6641H77.5892Z"
              fill="#D8F2D8"
            />
            <path
              d="M96.1893 42.6061L102.401 54.2343L99.0395 56.0285L98.2106 54.4727C98.0289 56.1193 96.9615 57.5047 95.4285 58.3223C92.317 59.9802 88.5129 58.6289 86.6619 55.1654C84.8223 51.7247 85.7989 47.7843 88.9103 46.1263C90.4433 45.3087 92.1694 45.1384 93.6343 45.8992L92.828 44.3889L96.1893 42.5947V42.6061ZM94.6676 54.768C96.3369 53.8709 96.9388 51.8269 96.053 50.1576C95.1559 48.4883 93.1233 47.8524 91.454 48.7381C89.7847 49.6352 89.1828 51.6793 90.0799 53.3485C90.977 55.0178 93.0097 55.6537 94.679 54.768H94.6676Z"
              fill="#D8F2D8"
            />
            <path
              d="M108.715 41.8913C108.476 41.8686 108.158 41.9367 107.738 42.0957C105.717 42.8565 105.081 44.8097 106.001 47.2284L107.897 52.2476L104.275 53.6217L99.6074 41.2894L103.207 39.9268L103.968 41.9481C104.104 40.2674 105.047 38.9161 106.648 38.3143C106.92 38.2121 107.216 38.1553 107.307 38.1553L108.726 41.9026L108.715 41.8913Z"
              fill="#D8F2D8"
            />
            <path
              d="M123.717 44.4119C123.183 46.7739 121.434 48.5908 118.629 49.2835C114.643 50.2828 111.157 48.2047 110.158 44.2416C109.193 40.3806 111.328 36.7923 115.109 35.8497C118.97 34.8845 122.343 37.0421 123.342 41.062C123.455 41.5049 123.501 41.8228 123.524 42.0045L114.121 44.3551C114.621 45.82 116.267 46.5695 117.846 46.172C119.265 45.82 119.844 45.0365 120.208 43.935L123.705 44.4119H123.717ZM119.265 40.4601C118.743 39.1315 117.38 38.4275 115.824 38.8136C114.325 39.1883 113.36 40.6418 113.542 41.8909L119.265 40.4601Z"
              fill="#D8F2D8"
            />
            <path
              d="M136.445 46.2982L128.53 27.9248L132.823 27.7544L137.933 40.1321L142.044 27.3797L146.2 27.2207L139.738 46.1733L136.434 46.3096L136.445 46.2982Z"
              fill="#D8F2D8"
            />
            <path
              d="M158.27 44.219C156.953 46.2516 154.693 47.3645 151.832 47.0692C147.744 46.6377 145.177 43.5035 145.598 39.4496C146.006 35.4978 149.231 32.8519 153.115 33.2494C157.067 33.6582 159.508 36.8378 159.077 40.9485C159.031 41.4027 158.975 41.7207 158.929 41.9024L149.288 40.9031C149.265 42.4588 150.549 43.7193 152.173 43.8896C153.637 44.0373 154.444 43.5035 155.148 42.5951L158.282 44.2303L158.27 44.219ZM155.432 38.984C155.398 37.5532 154.353 36.429 152.763 36.27C151.219 36.111 149.833 37.1444 149.561 38.3821L155.432 38.9953V38.984Z"
              fill="#D8F2D8"
            />
            <path
              d="M169.547 40.4384C169.365 40.2908 169.058 40.1659 168.616 40.0637C166.503 39.5867 164.902 40.8586 164.334 43.3909L163.142 48.6145L159.372 47.7628L162.29 34.9082L166.038 35.7599L165.561 37.8607C166.594 36.5321 168.127 35.9302 169.797 36.3049C170.08 36.3731 170.353 36.4866 170.432 36.5321L169.547 40.4384Z"
              fill="#D8F2D8"
            />
            <path
              d="M166.334 54.9053L172.795 37.3154L176.429 38.6554L175.816 40.3133C177.167 39.3594 178.916 39.2345 180.54 39.8364C183.844 41.0514 185.366 44.7988 184.014 48.478C182.674 52.1346 179.075 54.031 175.77 52.8159C174.146 52.2141 172.863 51.0671 172.466 49.4433L169.956 56.2567L166.322 54.9167L166.334 54.9053ZM178.393 42.7548C176.61 42.0962 174.68 43.016 174.033 44.7875C173.374 46.5703 174.26 48.5121 176.031 49.1594C177.814 49.818 179.733 48.8982 180.392 47.1267C181.051 45.3439 180.165 43.4021 178.393 42.7548Z"
              fill="#D8F2D8"
            />
            <path
              d="M201.037 50.397L194.553 61.8662L191.237 59.9926L192.1 58.4595C190.624 59.1863 188.875 59.0273 187.364 58.1643C184.298 56.4269 183.401 52.4865 185.332 49.0798C187.251 45.6958 191.1 44.4012 194.167 46.1387C195.677 46.9903 196.744 48.3644 196.881 50.0109L197.721 48.5233L201.037 50.397ZM189.965 55.6206C191.612 56.5518 193.667 55.9613 194.598 54.3147C195.529 52.6682 194.973 50.6128 193.326 49.6816C191.68 48.7505 189.624 49.341 188.693 50.9875C187.762 52.6341 188.307 54.6895 189.965 55.632V55.6206Z"
              fill="#D8F2D8"
            />
            <path
              d="M209.281 56.131C210.19 56.8237 210.985 57.7662 211.416 58.7996L209.202 61.7067C208.873 60.6506 208.305 59.8103 207.521 59.2084C205.886 57.9593 203.819 58.1637 202.604 59.7535C201.389 61.3433 201.741 63.3873 203.376 64.6364C204.205 65.2723 205.25 65.5676 206.283 65.5789L204.046 68.5087C202.831 68.3497 201.753 67.8274 200.833 67.1347C197.608 64.6705 196.972 60.6733 199.481 57.3802C201.889 54.2233 206 53.6328 209.293 56.131H209.281Z"
              fill="#D8F2D8"
            />
            <path
              d="M223.122 69.3373L215.151 69.678L214.117 78.9669L210.79 75.6851L211.608 69.1102H210.665L207.44 72.3806L204.681 69.6666L217.785 56.3691L220.545 59.0831L213.39 66.3394L219.806 66.0669L223.111 69.3259L223.122 69.3373Z"
              fill="#D8F2D8"
            />
            <path
              d="M229.766 77.8896L232.128 80.9897L221.636 88.9727L219.274 85.8726L220.614 84.8506C219.149 84.9982 217.729 84.3964 216.673 83.0223C214.845 80.6263 215.379 77.935 218.036 75.9137L224.509 70.9854L226.871 74.0854L221.136 78.446C219.762 79.5021 219.455 80.7739 220.352 81.9549C221.261 83.1473 222.714 83.2495 224.225 82.1025L229.766 77.8896Z"
              fill="#D8F2D8"
            />
            <path
              d="M224.508 93.9235L222.635 90.5054L234.195 84.1689L236.069 87.587L234.592 88.3932C236.114 88.5068 237.386 89.2676 238.249 90.8347C239.702 93.4806 238.783 96.081 235.864 97.6822L228.733 101.6L226.859 98.1818L233.196 94.707C234.717 93.878 235.171 92.6516 234.445 91.3116C233.729 90.0057 232.264 89.6764 230.652 90.5622L224.52 93.9235H224.508Z"
              fill="#D8F2D8"
            />
            <path
              d="M234.637 111.4C233.229 111.025 232.003 109.833 231.412 108.198C230.197 104.836 231.98 101.384 235.523 100.101C239.134 98.7952 242.802 100.305 244.017 103.644C244.63 105.325 244.437 107.073 243.483 108.209L245.096 107.63L246.402 111.241L234.808 115.431C230.924 116.839 227.358 115.318 225.791 110.98C224.735 108.039 225.167 105.336 226.847 103.621L229.629 105.904C228.641 107.028 228.369 108.459 228.88 109.867C229.607 111.877 231.299 112.626 233.502 111.831L234.66 111.411L234.637 111.4ZM241.099 105.711C240.485 104.007 238.612 103.144 236.852 103.78C235.091 104.416 234.206 106.267 234.819 107.982C235.432 109.685 237.317 110.571 239.077 109.935C240.837 109.299 241.712 107.425 241.099 105.711Z"
              fill="#D8F2D8"
            />
            <path
              d="M239.905 129.023C237.577 128.364 235.851 126.536 235.295 123.709C234.5 119.677 236.737 116.293 240.746 115.51C244.641 114.738 248.116 117.043 248.876 120.87C249.649 124.765 247.321 128.024 243.267 128.83C242.824 128.921 242.506 128.955 242.313 128.966L240.439 119.462C238.952 119.893 238.123 121.494 238.441 123.095C238.724 124.538 239.474 125.151 240.553 125.56L239.905 129.034V129.023ZM244.073 124.776C245.424 124.322 246.196 122.993 245.89 121.426C245.595 119.905 244.186 118.883 242.937 118.996L244.084 124.776H244.073Z"
              fill="#D8F2D8"
            />
            <path
              d="M237.171 134.906L236.978 131.011L250.15 130.363L250.343 134.258L248.663 134.338C249.98 135.099 250.797 136.348 250.877 138.131C251.024 141.14 249.06 143.082 245.733 143.252L237.602 143.649L237.409 139.754L244.631 139.402C246.369 139.312 247.311 138.414 247.232 136.893C247.164 135.405 245.994 134.474 244.154 134.565L237.171 134.906Z"
              fill="#D8F2D8"
            />
            <path
              d="M246.232 153.71C246.084 155.016 244.915 155.959 243.643 155.811C242.371 155.675 241.417 154.551 241.565 153.188C241.713 151.837 242.859 150.94 244.154 151.087C245.426 151.223 246.38 152.382 246.232 153.699V153.71Z"
              fill="#D8F2D8"
            />
            <path
              d="M50.9271 165.52L36.5168 169.37L38.1974 175.661L34.5636 176.626L31.8496 166.474L49.8937 161.659L50.9271 165.531V165.52Z"
              fill="#D8F2D8"
            />
            <path
              d="M35.4717 178.874L47.8493 174.321L49.1893 177.977L36.823 182.531L35.4717 178.874ZM49.9729 175.615C49.53 174.412 50.1886 173.117 51.3242 172.708C52.4597 172.299 53.7884 172.856 54.2312 174.06C54.6741 175.263 54.0155 176.558 52.8799 176.967C51.7443 177.375 50.4044 176.819 49.9729 175.615Z"
              fill="#D8F2D8"
            />
            <path
              d="M52.3229 190.696L42.7046 188.22L45.4754 194.057L42.6024 195.42L37.6514 184.973L39.8657 183.917L49.3931 186.369L46.8494 181.021L49.7224 179.658L54.4691 189.663L52.3229 190.685V190.696Z"
              fill="#D8F2D8"
            />
            <path
              d="M53.2319 206.48C50.8245 206.73 48.5421 205.651 46.9863 203.221C44.7606 199.757 45.6123 195.794 49.0531 193.591C52.403 191.445 56.4797 192.308 58.5805 195.59C60.7267 198.94 59.7614 202.824 56.2866 205.061C55.9005 205.31 55.6166 205.458 55.4463 205.538L50.2113 197.384C48.9849 198.338 48.8032 200.132 49.6776 201.495C50.4725 202.733 51.3923 203.028 52.5506 203.005L53.2206 206.469L53.2319 206.48ZM55.5485 200.995C56.6386 200.075 56.8658 198.565 56.0027 197.214C55.1624 195.908 53.4818 195.465 52.3576 196.033L55.5485 200.995Z"
              fill="#D8F2D8"
            />
            <path
              d="M55.3896 214.1L52.8232 211.17L62.7594 202.506L65.3258 205.436L64.054 206.537C65.5643 206.321 66.9724 206.787 68.142 208.138C70.1179 210.409 69.7886 213.146 67.2676 215.338L61.1356 220.686L58.5806 217.745L64.0313 212.998C65.3372 211.863 65.5189 210.568 64.5196 209.421C63.5316 208.297 62.044 208.286 60.6586 209.501L55.3896 214.089V214.1Z"
              fill="#D8F2D8"
            />
            <path
              d="M79.0313 222.173L69.2314 223.751L74.1029 227.998L72.0135 230.394L63.3037 222.797L64.9162 220.946L74.6253 219.391L70.1625 215.496L72.252 213.1L80.5984 220.379L79.0427 222.173H79.0313Z"
              fill="#D8F2D8"
            />
            <path
              d="M74.1836 231.894L81.8941 221.197L85.051 223.479L77.3405 234.176L74.1836 231.894ZM84.381 221.083C83.3363 220.334 83.1659 218.892 83.87 217.915C84.574 216.939 86.0048 216.655 87.0382 217.404C88.0829 218.154 88.2533 219.596 87.5492 220.572C86.8452 221.549 85.4143 221.833 84.381 221.083Z"
              fill="#D8F2D8"
            />
            <path
              d="M92.6023 239.774C90.581 241.102 88.06 241.193 85.5618 239.751C81.9961 237.707 80.9287 233.789 82.9613 230.246C84.9486 226.806 88.9685 225.693 92.3411 227.646C95.7932 229.622 96.7244 233.517 94.6576 237.105C94.4305 237.503 94.2488 237.764 94.1353 237.912L85.7435 233.085C85.0848 234.493 85.7435 236.174 87.1629 236.98C88.4347 237.707 89.3886 237.548 90.4106 237.003L92.6023 239.774ZM92.1367 233.846C92.6818 232.529 92.1935 231.075 90.8081 230.281C89.4681 229.508 87.7761 229.883 87.0266 230.905L92.1367 233.846Z"
              fill="#D8F2D8"
            />
            <path
              d="M105.479 238.503C105.32 238.321 105.048 238.151 104.639 237.969C102.652 237.118 100.846 238.094 99.8241 240.468L97.7233 245.396L94.1689 243.874L99.3471 231.758L102.879 233.268L102.027 235.255C103.288 234.143 104.9 233.813 106.478 234.483C106.751 234.597 107.001 234.767 107.058 234.824L105.491 238.503H105.479Z"
              fill="#D8F2D8"
            />
            <path
              d="M113.916 233.176L112.747 236.867L115.926 237.866L114.961 240.921L111.782 239.922L110.328 244.521C109.897 245.872 110.328 246.588 111.543 246.974C112.031 247.121 112.542 247.178 113.042 247.03L112.031 250.233C111.361 250.324 110.476 250.301 109.272 249.926C106.626 249.086 105.502 246.974 106.399 244.146L108.102 238.752L106.32 238.184L107.285 235.13L109.068 235.697L110.237 232.007L113.928 233.176H113.916Z"
              fill="#D8F2D8"
            />
            <path
              d="M120.867 239.207L124.944 239.775L126.863 248.451L131.076 240.626L135.039 241.183L127.896 253.504L124.353 253.004L120.867 239.207Z"
              fill="#D8F2D8"
            />
            <path
              d="M141.773 240.809C145.827 240.809 148.927 243.806 148.938 247.736C148.938 251.653 145.861 254.685 141.795 254.685C137.741 254.685 134.641 251.687 134.63 247.758C134.63 243.829 137.707 240.809 141.773 240.809ZM141.795 251.131C143.612 251.131 145.1 249.632 145.1 247.736C145.1 245.839 143.601 244.352 141.784 244.352C139.967 244.352 138.479 245.851 138.479 247.747C138.479 249.643 139.978 251.131 141.795 251.131Z"
              fill="#D8F2D8"
            />
            <path
              d="M156.306 253.379L152.457 253.98L150.436 240.955L154.285 240.354L154.546 242.012C155.171 240.615 156.318 239.684 158.089 239.411C161.064 238.946 163.199 240.694 163.71 243.999L164.959 252.039L161.11 252.64L160.008 245.498C159.747 243.783 158.748 242.943 157.249 243.17C155.773 243.397 154.966 244.657 155.25 246.474L156.318 253.379H156.306Z"
              fill="#D8F2D8"
            />
            <path
              d="M172.296 230.44L177.293 244.499L183.425 242.318L184.685 245.861L174.783 249.382L168.515 231.78L172.285 230.44H172.296Z"
              fill="#D8F2D8"
            />
            <path
              d="M182.402 230.6C181.232 231.133 179.892 230.577 179.392 229.487C178.893 228.397 179.347 227.011 180.505 226.477C181.675 225.944 183.015 226.5 183.515 227.59C184.014 228.68 183.56 230.066 182.402 230.6ZM186.796 244.794L181.277 232.814L184.82 231.179L190.339 243.159L186.796 244.794Z"
              fill="#D8F2D8"
            />
            <path
              d="M197.257 227.057L195.565 236.834L201.163 233.609L202.741 236.357L192.737 242.126L191.511 240.002L193.191 230.316L188.059 233.269L186.469 230.52L196.064 224.99L197.245 227.046L197.257 227.057Z"
              fill="#D8F2D8"
            />
            <path
              d="M212.88 224.899C213.323 227.284 212.437 229.635 210.144 231.383C206.873 233.882 202.853 233.359 200.378 230.111C197.97 226.955 198.504 222.821 201.604 220.448C204.772 218.04 208.713 218.688 211.222 221.969C211.495 222.333 211.665 222.605 211.767 222.764L204.068 228.635C205.113 229.782 206.919 229.816 208.213 228.828C209.383 227.943 209.598 227 209.485 225.842L212.892 224.888L212.88 224.899ZM207.225 223.037C206.215 222.026 204.693 221.913 203.421 222.889C202.195 223.832 201.888 225.535 202.535 226.614L207.225 223.037Z"
              fill="#D8F2D8"
            />
            <path
              d="M220.308 222.117L217.594 224.91L208.146 215.712L210.86 212.919L212.064 214.088C211.724 212.601 212.087 211.159 213.325 209.875C215.425 207.718 218.185 207.831 220.581 210.159L226.406 215.837L223.692 218.631L218.514 213.589C217.276 212.374 215.971 212.306 214.903 213.396C213.858 214.463 213.972 215.951 215.301 217.245L220.308 222.117Z"
              fill="#D8F2D8"
            />
            <path
              d="M226.463 197.861L228.825 207.502L232.663 202.312L235.218 204.197L228.348 213.497L226.372 212.044L224.044 202.494L220.524 207.263L217.969 205.378L224.555 196.464L226.463 197.872V197.861Z"
              fill="#D8F2D8"
            />
            <path
              d="M239.931 189.65C241.282 191.66 241.407 194.181 240.01 196.691C238.012 200.279 234.105 201.415 230.54 199.416C227.065 197.474 225.906 193.477 227.803 190.07C229.745 186.595 233.617 185.608 237.228 187.629C237.626 187.856 237.887 188.026 238.046 188.14L233.322 196.6C234.741 197.236 236.41 196.543 237.205 195.124C237.921 193.84 237.75 192.887 237.183 191.876L239.919 189.639L239.931 189.65ZM234.003 190.195C232.674 189.673 231.232 190.184 230.449 191.581C229.699 192.932 230.097 194.624 231.13 195.351L234.003 190.195Z"
              fill="#D8F2D8"
            />
            <path
              d="M238.488 176.786C238.307 176.945 238.136 177.217 237.966 177.637C237.148 179.636 238.136 181.43 240.532 182.418L245.495 184.451L244.03 188.028L231.834 183.008L233.299 179.454L235.297 180.272C234.162 179.023 233.821 177.41 234.469 175.832C234.582 175.559 234.741 175.309 234.798 175.241L238.5 176.763L238.488 176.786Z"
              fill="#D8F2D8"
            />
            <path
              d="M236.729 168.302C237.819 164.396 241.543 162.216 245.325 163.261C249.106 164.305 251.184 168.098 250.105 172.004C249.015 175.911 245.291 178.091 241.509 177.046C237.728 176.002 235.65 172.209 236.729 168.302ZM246.687 171.062C247.176 169.313 246.131 167.474 244.303 166.963C242.474 166.452 240.635 167.496 240.158 169.245C239.67 170.994 240.714 172.833 242.543 173.344C244.371 173.855 246.211 172.811 246.687 171.062Z"
              fill="#D8F2D8"
            />
            <defs>
              <clipPath id="clip0_4128_82138">
                <rect width="113.327" height="113.327" fill="white" transform="translate(83.2881 86.0195)" />
              </clipPath>
            </defs>
          </svg>
        </div>
        <div className="flex-1 flex flex-col z-10">
          <span className="text-title-1 text-support-blue leading-none font-bold mb-5">2023-2024</span>
          <div className="space-y-2 mb-10">
            <h3 className="text-[60px] text-primary font-bold leading-none">{resourcesSaved} kg</h3>
            <p className="text-title-3 text-primary font-bold">Resources saved</p>
          </div>
          <div className="flex items-center justify-end gap-10">
            <Image src="/assets/images/resources-saved.png" alt="Resources saved" width={97} height={57} />
            <Image src="/assets/images/fraunhofer.png" alt="Fraunhofer" width={97} height={57} />
          </div>
        </div>
      </div>
      <Link href="/saas/direct-license/sustainability" className="flex items-end gap-2">
        <p className="text-large-paragraph-regular text-support-blue font-bold underline underline-offset-2">
          Go to Sustainability{" "}
        </p>
        <BiLinkExternal className="size-6 fill-support-blue" />
      </Link>
    </div>
  );
}
