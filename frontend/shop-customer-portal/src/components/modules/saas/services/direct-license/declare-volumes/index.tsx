import { Divider } from "@/components/_common/divider";
import { TitleAndSubTitle } from "@/components/_common/titleAndSubTitle";
import { TooltipIcon } from "@/components/_common/tooltipIcon";
import { COUNTRIES } from "@/utils/consts/countries";
import NextImage from "next/image";
import { SaasBreadcrumb } from "../../../components/saas-breadcrumb";
import { SaasContainer } from "../../../components/saas-container";
import { DirectLicenseVolumes } from "./direct-license-volumes";

const germany = COUNTRIES.find((country) => country.code === "DE")!;

const paths = [
  { label: "Dashboard EU", href: "./" },
  { label: "Declare Volumes", href: "" },
];

export async function DirectLicenseDeclareVolumes() {
  return (
    <>
      <SaasBreadcrumb paths={paths} />
      <SaasContainer>
        <TitleAndSubTitle showIcon title="Declare Volumes" subText="Lorem ipsum dolor sit amet consectetur. " />
      </SaasContainer>
      <SaasContainer className="pt-4 mt:pt-0 pb-24" containerClassName="max-md:bg-[#F7F5F2]">
        <div className="space-y-10">
          <Divider initialMarginDisabled className="hidden md:block" />
          <div className="w-full flex flex-col gap-2">
            <div className="flex items-center gap-3">
              <NextImage
                src={germany.flag_url}
                width={28}
                height={28}
                alt="Germany's flag"
                className="size-7 rounded-full object-cover"
              />
              <p className="text-primary text-3xl font-bold">Germany</p>
            </div>
            <div className="flex items-center gap-3">
              <p className="text-base text-primary">Reports should be done Annualy</p>
              <TooltipIcon info="Reports should be done Annualy" />
            </div>
          </div>
          <DirectLicenseVolumes />
        </div>
      </SaasContainer>
    </>
  );
}
