import { Spinner } from "@/components/ui/loader";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import { useTranslations } from "next-intl";

interface DeclareVolumesLoadingProps {
  open: boolean;
  isRefund: boolean;
}

export function DeclareVolumesLoading({ open, isRefund }: DeclareVolumesLoadingProps) {
  const t = useTranslations("shop.common");

  return (
    <Modal open={open} className="z-50 w-full" style={{ maxWidth: "400px", borderRadius: "52px" }}>
      <div className="p-5">
        <div className="text-primary">
          <div className="flex flex-col items-center justify-center gap-4">
            <Spinner size="md" />
            <p className="text-center">
              {isRefund ? t("refundingVolume.description") : t("changingVolume.description")}
            </p>
          </div>
        </div>
      </div>
    </Modal>
  );
}
