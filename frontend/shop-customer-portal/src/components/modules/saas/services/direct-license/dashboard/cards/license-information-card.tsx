"use client";

import { useCustomer } from "@/hooks/use-customer";
import { formatDateToDDMMYYYY } from "@/utils/formatDateToDDMMYYYY";
import { LucidNumberInput } from "../../components/lucid-number-input";

interface DirectLicenseInformationCardProps {}

export function DirectLicenseInformationCard({}: DirectLicenseInformationCardProps) {
  const { customer } = useCustomer();

  const directLicenseContract = customer?.contracts.find((c) => c.type === "DIRECT_LICENSE");

  const directLicense = directLicenseContract?.licenses.find((license) => license.country_code === "DE");

  if (!directLicense) return null;

  return (
    <div id="information" className="col-span-1 md:col-span-6 px-4 py-6 md:p-8 bg-background rounded-3xl">
      <h2 className="text-title-3 text-primary font-bold mb-5">Licensing Information</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 w-full">
        <div className="w-full md:col-span-1 space-y-2 py-4">
          <label className="text-small-paragraph-regular text-tonal-dark-cream-30">Licensing date:</label>
          <p className="text-paragraph-regular text-primary">{formatDateToDDMMYYYY(directLicense.start_date)}</p>
        </div>
        <div className="w-full md:col-span-1 space-y-2 py-4">
          <label className="text-small-paragraph-regular text-tonal-dark-cream-30">Auto renew:</label>
          <p className="text-paragraph-regular text-primary">
            {directLicense.end_date ? formatDateToDDMMYYYY(directLicense.end_date) : "-------"}
          </p>
        </div>
        <div className="w-full md:col-span-1 space-y-2 py-4">
          <label className="text-small-paragraph-regular text-tonal-dark-cream-30">Type of license</label>
          <p className="text-paragraph-regular text-primary underline underline-offset-2">Direct License</p>
        </div>
        <div className="relative w-full md:col-span-2 py-4">
          <LucidNumberInput />
        </div>
      </div>
    </div>
  );
}
