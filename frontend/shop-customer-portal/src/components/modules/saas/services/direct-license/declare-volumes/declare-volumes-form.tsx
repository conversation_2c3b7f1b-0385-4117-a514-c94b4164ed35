"use client";

import { FractionInput } from "@/components/_common/forms/fraction-input";
import {
  QuestionTooltip,
  QuestionTooltipDescription,
  QuestionTooltipTitle,
} from "@/components/_common/question-tooltip";
import { TooltipIcon } from "@/components/_common/tooltipIcon";
import { Skeleton } from "@/components/ui/skeleton";
import { useRouter } from "@/i18n/navigation";
import { ContractType } from "@/lib/api/contracts/types";
import { getLicense } from "@/lib/api/license";
import { PackagingService } from "@/lib/api/packaging-services/types";
import { createPurchase } from "@/lib/api/purchase";
import { createShoppingCart, updateShoppingCart } from "@/lib/api/shoppingCart";
import { PriceList } from "@/lib/api/shoppingCart/types";
import { queryClient } from "@/lib/react-query";
import { useCustomer } from "@/hooks/use-customer";
import { calculateDirectLicenseNetValue } from "@/utils/calculate-germany-total-amount";
import { GERMANY_FRACTIONS } from "@/utils/consts/direct-license";
import { formatWeight } from "@/utils/format-weight";
import { formatCurrency } from "@/utils/formatCurrency";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Aluminium, East } from "@arthursenno/lizenzero-ui-react/Icon";
import { zodResolver } from "@hookform/resolvers/zod";
import { useQuery } from "@tanstack/react-query";
import NextImage from "next/image";
import { enqueueSnackbar } from "notistack";
import { useEffect, useState } from "react";
import { Controller, useForm, useWatch } from "react-hook-form";
import { CgSpinnerAlt } from "react-icons/cg";

import { z } from "zod";
import { DeclareVolumesLoading } from "./declare-volumes-loading";
import { DirectLicenseReportType } from "./direct-license-volumes";

const directLicenseDeclareVolumesFormSchema = z.object({
  fractions: z.record(
    z.string(),
    z.object({
      code: z.string(),
      name: z.string(),
      weight: z.number().gte(0, { message: "The minimum weight is 0." }).optional().default(0),
      price: z.number(),
    })
  ),
});

export type DirectLicenseDeclareVolumesFormData = z.infer<typeof directLicenseDeclareVolumesFormSchema>;

interface DirectLicenseDeclareVolumesFormsProps {
  reportType: DirectLicenseReportType;
  licenseYear: number;
  packagingService: PackagingService | null | undefined;
}

export function DirectLicenseDeclareVolumesForm({ reportType, licenseYear }: DirectLicenseDeclareVolumesFormsProps) {
  const router = useRouter();
  const { customer } = useCustomer();

  const currentYear = new Date().getFullYear();

  const directLicenseContract = customer?.contracts.find((contract) => contract.type === "DIRECT_LICENSE");

  const firstDirectLicense = directLicenseContract?.licenses[0] || null;
  const pastDirectLicenseId =
    directLicenseContract?.licenses.find((license) => license.year === licenseYear - 1)?.id || null;
  const currentDirectLicenseId =
    directLicenseContract?.licenses.find((license) => license.year === licenseYear)?.id || null;

  const pastDirectLicenseQuery = useQuery({
    queryKey: ["license", pastDirectLicenseId],
    queryFn: () => getLicense(pastDirectLicenseId || 0),
    enabled: !!pastDirectLicenseId,
  });

  const pastDirectLicense = pastDirectLicenseQuery.data || null;
  const pastVolumeItems = pastDirectLicense?.packaging_services[0].volume_reports[0].volume_report_items || null;

  const currentDirectLicenseQuery = useQuery({
    queryKey: ["license", currentDirectLicenseId],
    queryFn: () => getLicense(currentDirectLicenseId || 0),
    enabled: !!currentDirectLicenseId,
  });

  const currentDirectLicense = currentDirectLicenseQuery.data || null;
  const currentVolumeItems = currentDirectLicense?.packaging_services[0].volume_reports[0].volume_report_items || null;
  const [isChangingVolume, setIsChangingVolume] = useState(false);

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<DirectLicenseDeclareVolumesFormData>({
    resolver: zodResolver(directLicenseDeclareVolumesFormSchema),
  });

  const fractions = useWatch({ control, name: "fractions" }) || [];
  const fractionList = Object.values(fractions);

  const netValue = calculateDirectLicenseNetValue({
    fractions: fractionList,
    currentFractions: !!currentVolumeItems
      ? currentVolumeItems.map((item) => ({
          name: item.setup_fraction_code,
          code: item.setup_fraction_code,
          weight: item.value,
        })) || null
      : null,
    priceList: firstDirectLicense?.price_list?.[0] as PriceList,
  });

  const isRefund = reportType === "VOLUME_CHANGE" && netValue < 0;

  async function handleFormSubmit(data: DirectLicenseDeclareVolumesFormData) {
    try {
      if (!customer) return;

      setIsChangingVolume(true);

      const cart = await createShoppingCart({
        journey: "DIRECT_LICENSE",
        email: customer?.email,
        force: true,
      });

      await updateShoppingCart(cart.id, {
        journey: "DIRECT_LICENSE",
        email: customer?.email,
        items: [
          {
            country_id: 5,
            country_code: "DE",
            country_name: "Germany",
            country_flag: "https://cdn.kcak11.com/CountryFlags/countries/de.svg",
            service_type: "DIRECT_LICENSE" as ContractType,
            specification_type: "VOLUME_CHANGE",
            year: licenseYear,
            packaging_services: [
              {
                id: 0,
                name: `Direct License ${licenseYear}`,
                fractions: data.fractions,
              },
            ],
          },
        ],
      });

      if (isRefund) {
        const paymentResponse = await createPurchase({ shopping_cart_id: cart.id });

        if (!paymentResponse.success) throw new Error("Error creating payment");

        queryClient.invalidateQueries({
          predicate: (query) => {
            return (
              query.queryKey.includes("customer") ||
              query.queryKey.includes("contract") ||
              query.queryKey.includes("license") ||
              query.queryKey.includes("packaging-service") ||
              query.queryKey.includes("order")
            );
          },
        });

        enqueueSnackbar("Refund order created successfully!", { variant: "success" });

        return router.push(`/saas/invoices-and-payment?license-year=${licenseYear}`);
      }

      router.push("/direct-license/purchase");
    } catch (err: any) {
      setIsChangingVolume(false);
      enqueueSnackbar("Failed to proceed with volume report. Please, try again.", { variant: "error" });
    }
  }

  useEffect(() => {
    reset({
      fractions: GERMANY_FRACTIONS.reduce(
        (acc, fraction) => {
          acc[fraction.code] = {
            code: fraction.code,
            name: fraction.name,
            weight: 0,
            price: 0,
          };
          return acc;
        },
        {} as Record<string, { code: string; name: string; weight: number; price: number }>
      ),
    });
  }, [licenseYear]);

  const hasNewValue = fractionList.some((item) => item.weight > 0);

  return (
    <div className="space-y-10 md:space-y-14">
      <div className="hidden md:flex gap-6">
        <form onSubmit={handleSubmit(handleFormSubmit)}>
          <div className="flex flex-col w-full rounded-2xl  border-[1px] border-[#EDE9E4] overflow-hidden">
            <div className="flex items-center bg-[#EDE9E4] w-full border-b-[1px] border-white">
              <div className="text-primary font-sm font-normal flex-none px-2 py-4 w-56 text-left"></div>
              <div className="text-primary font-sm font-normal flex-none px-2 py-4 w-48 text-left">
                {reportType === "INITIAL_PLANNED" ? `${licenseYear - 1} Volumes` : "Current Volume"}
              </div>
              <div className="text-primary font-sm font-normal flex-none px-2 py-4 w-48 text-left">
                {reportType === "INITIAL_PLANNED" ? `Initial planned volume (${licenseYear})` : "Volume change"}
              </div>
            </div>
            <div className="hidden md:flex flex-col">
              {GERMANY_FRACTIONS.map((fraction, fractionIndex) => (
                <div
                  key={`fraction-${fractionIndex}`}
                  className="w-full flex items-stretch border-b-[1px] border-white"
                >
                  <div className="flex-none px-2 py-4 w-56 bg-[#F0F0EF]">
                    <div className="flex items-center gap-1 w-full">
                      <QuestionTooltip>
                        <QuestionTooltipTitle>
                          <div className="size-6">
                            <NextImage
                              src="/assets/svg/aluminium.svg"
                              alt={fraction.name}
                              width={100}
                              height={100}
                              className=""
                            />
                          </div>
                          <p className="text-primary text-md font-bold">{fraction.name}</p>
                        </QuestionTooltipTitle>
                        <QuestionTooltipDescription className="text-primary">
                          Bottle tops, film for chocolate and tubes for skincare products: packaging made from aluminium
                          is mostly used to package food, cosmetics or pharmaceutical products. Enter the estimated
                          total weight of packaging that you will place on the German market in the respective licence
                          year.
                        </QuestionTooltipDescription>
                      </QuestionTooltip>
                      <div className="flex flex-1 items-center gap-3">
                        <Aluminium className="flex-none size-6 md:size-8 fill-primary" />
                        <p className="text-xs md:text-sm text-primary">{fraction.name}</p>
                      </div>
                    </div>
                  </div>
                  <div className="flex-none px-2 py-4 w-48">
                    <div className="flex h-full items-center gap-4 w-full flex-shrink-0">
                      {!currentDirectLicenseQuery.isLoading && (
                        <span className="text-base text-primary">
                          {reportType === "INITIAL_PLANNED" && (
                            <>
                              {formatWeight(
                                pastVolumeItems?.find((item) => item.setup_fraction_code === fraction.code)?.value || 0
                              )}
                            </>
                          )}
                          {reportType !== "INITIAL_PLANNED" && (
                            <>
                              {formatWeight(
                                currentVolumeItems?.find((item) => item.setup_fraction_code === fraction.code)?.value ||
                                  0
                              )}
                            </>
                          )}
                        </span>
                      )}
                      {currentDirectLicenseQuery.isLoading && <Skeleton className="h-10 w-full" />}
                    </div>
                  </div>
                  <div className="flex-none px-2 py-4 w-48">
                    <div className="flex h-full items-center gap-4 w-full flex-shrink-0">
                      <Controller
                        key={fraction.code}
                        name={`fractions.${fraction.code}.weight`}
                        disabled={currentDirectLicenseQuery.isLoading || isChangingVolume}
                        control={control}
                        render={({ field }) => (
                          <FractionInput
                            {...field}
                            type="weight"
                            value={field.value}
                            data-invalid={!!errors && !!errors.fractions && !!errors.fractions[fractionIndex]}
                            onChange={(value) => field.onChange(value)}
                            className="h-10 flex-1"
                          />
                        )}
                      />
                      <span className="text-base text-primary">kg</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
          <div className="w-full flex justify-end h-20">
            <div className="flex justify-end gap-5 flex-col">
              <Button
                type="submit"
                variant="filled"
                color="yellow"
                size="medium"
                disabled={isChangingVolume || !hasNewValue}
                trailingIcon={<East />}
              >
                {isChangingVolume ? "Loading..." : "Save changes"}
              </Button>
            </div>
          </div>
        </form>
        <div className="flex items-end pb-16 flex-none">
          <div className="bg-tonal-dark-blue-96 py-4 px-6 rounded-3xl flex flex-col gap-4">
            <div className="flex items-center gap-4">
              <p className="font-bold text-primary text-title-3">Your net price</p>
              <TooltipIcon info="Your net price" />
            </div>
            <div className="flex flex-col gap-2">
              <p className="text-support-blue font-bold text-4xl">{formatCurrency(netValue || 0)}</p>
              <p className="text-sm text-tonal-dark-cream-40">/ per year plus VAT</p>
            </div>
          </div>
        </div>
      </div>
      <form className="block md:hidden space-y-6">
        <div className="bg-white rounded-2xl space-y-2">
          {GERMANY_FRACTIONS.map((fraction, fractionIndex) => (
            <div key={fraction.code}>
              <div className="p-4 pb-1 space-y-3">
                <div className="flex items-center gap-2">
                  <Aluminium className="flex-none size-5 fill-primary" />
                  <p className="flex-1 text-primary">{fraction.name}</p>
                  <QuestionTooltip>
                    <QuestionTooltipTitle>
                      <div className="size-6">
                        <NextImage
                          src="/assets/svg/aluminium.svg"
                          alt={fraction.name}
                          width={100}
                          height={100}
                          className=""
                        />
                      </div>
                      <p className="text-primary text-md font-bold">{fraction.name}</p>
                    </QuestionTooltipTitle>
                    <QuestionTooltipDescription className="text-primary">
                      Bottle tops, film for chocolate and tubes for skincare products: packaging made from aluminium is
                      mostly used to package food, cosmetics or pharmaceutical products. Enter the estimated total
                      weight of packaging that you will place on the German market in the respective licence year.
                    </QuestionTooltipDescription>
                  </QuestionTooltip>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  {["VOLUME_CHANGE", "END_OF_YEAR"].includes(reportType) ? (
                    <p className="text-tonal-dark-cream-40">Current volume:</p>
                  ) : (
                    <p className="text-tonal-dark-cream-40">{currentYear} volume:</p>
                  )}
                  <p className="text-tonal-dark-cream-20">
                    {formatWeight(
                      currentVolumeItems?.find((item) => item.setup_fraction_code === fraction.code)?.value || 0
                    )}
                  </p>
                </div>
              </div>
              <div className="px-4 py-3">
                <Controller
                  key={fraction.code}
                  name={`fractions.${fraction.code}.weight`}
                  disabled={currentDirectLicenseQuery.isLoading || isChangingVolume}
                  control={control}
                  render={({ field }) => (
                    <FractionInput
                      {...field}
                      type="weight"
                      value={field.value}
                      data-invalid={!!errors && !!errors.fractions && !!errors.fractions[fractionIndex]}
                      onChange={(value) => field.onChange(value)}
                      className="h-10 flex-1"
                    />
                  )}
                />
              </div>
            </div>
          ))}
        </div>
        <div className="space-y-4">
          <p className="font-bold text-support-blue text-lg">Your net price</p>
          <div className="flex flex-col gap-2">
            <p className="text-primary font-bold text-lg">{formatCurrency(netValue || 0)}</p>
            <p className="text-sm text-tonal-dark-cream-10">/ per year plus VAT</p>
          </div>
        </div>
        <div className="w-full h-[1px] bg-tonal-dark-cream-80"></div>
        <Button
          type="submit"
          variant="filled"
          color="yellow"
          size="medium"
          disabled={isChangingVolume || !hasNewValue}
          trailingIcon={isChangingVolume ? <CgSpinnerAlt className="animate-spin" /> : <East />}
          className="w-full"
        >
          {isChangingVolume ? "Loading..." : "Save changes"}
        </Button>
      </form>
      {reportType !== "INITIAL_PLANNED" && (
        <p className="hidden md:block text-tonal-dark-cream-40 text-base">
          Please note: that we do not issue credit notes if you reduce your packaging volumes aftes 01.09.{licenseYear}
        </p>
      )}
      <DeclareVolumesLoading open={isChangingVolume} isRefund={isRefund} />
    </div>
  );
}
