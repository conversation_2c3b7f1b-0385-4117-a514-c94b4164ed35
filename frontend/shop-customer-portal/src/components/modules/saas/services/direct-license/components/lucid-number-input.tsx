"use client";

import { enqueueSnackbar } from "notistack";
import { useState } from "react";
import { BiLoaderCircle } from "react-icons/bi";

import { useCustomer } from "@/hooks/use-customer";
import { updateCompany } from "@/lib/api/company";
import { cn } from "@/lib/utils";
import { validateLUCIDNumber } from "@/utils/validateLucidNumber";

interface LucidNumberInputProps {
  label?: string;
  errorMessage?: React.ReactNode;
}

export function LucidNumberInput({
  label = "LUCID number *",
  errorMessage = "LUCID number not valid",
}: LucidNumberInputProps) {
  const { customer, invalidateCustomer } = useCustomer();

  const [isLucidNumberValid, setIsLucidNumberValid] = useState<boolean>(true);
  const [lucidNumber, setLucidNumber] = useState<string>(customer?.company?.lucid || "");
  const [isLoadingLucid, setIsLoadingLucid] = useState(false);

  async function handleLucidNumberChange(event: React.ChangeEvent<HTMLInputElement>) {
    const lucidValue = event.target.value;
    setLucidNumber(lucidValue);

    const isLucidValid = lucidValue.length === 0 ? true : validateLUCIDNumber(lucidValue);
    setIsLucidNumberValid(isLucidValid);

    if (!customer?.company) return;

    if (!lucidValue.length || !isLucidValid || lucidValue === customer.company.lucid) return;

    setIsLoadingLucid(true);

    const res = await updateCompany(customer.company.id, { lucid: lucidValue });

    setIsLoadingLucid(false);

    if (!res.data) return enqueueSnackbar("Error updating LUCID number", { variant: "error" });

    enqueueSnackbar("LUCID number updated successfully", { variant: "success" });

    invalidateCustomer();
  }

  return (
    <div className="w-full space-y-2">
      <label className="text-small-paragraph-regular font-bold text-primary">{label}</label>
      <input
        type="text"
        value={lucidNumber}
        disabled={isLoadingLucid}
        onChange={handleLucidNumberChange}
        placeholder="Enter your LUCID number"
        maxLength={17}
        data-valid={isLucidNumberValid}
        className={cn(
          "block w-full md:w-10/12 p-4 rounded-2xl border-[1px]",
          isLucidNumberValid ? "border-support-blue text-primary" : "bg-error-opacity border-error text-error"
        )}
      />
      {!!lucidNumber.length && !isLucidNumberValid && <span className="text-sm text-error">{errorMessage}</span>}
      {isLoadingLucid && (
        <div className="inline-flex items-center gap-2">
          <BiLoaderCircle className="mt-0.5 size-4 text-primary animate-spin" />
          <span className="text-primary text-sm">Updating LUCID Number...</span>
        </div>
      )}
    </div>
  );
}
