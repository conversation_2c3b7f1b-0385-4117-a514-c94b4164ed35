import { cn } from "@/lib/utils";
import { ReactNode } from "react";

interface SaasTitleProps {
  children: ReactNode;
  className?: string;
}

export function SaasTitle({ children, className }: SaasTitleProps) {
  return (
    <h1
      className={cn(
        "text-primary gap-4 text-3xl md:text-[40px] md:leading-[52px] font-semibold md:font-bold flex items-center",
        className
      )}
    >
      {children}
    </h1>
  );
}

interface SaasDescriptionProps {
  children: ReactNode;
  className?: string;
}

export function SaasDescription({ children, className }: SaasDescriptionProps) {
  return <p className={cn("text-primary text-base leading-5 flex items-center", className)}>{children}</p>;
}
