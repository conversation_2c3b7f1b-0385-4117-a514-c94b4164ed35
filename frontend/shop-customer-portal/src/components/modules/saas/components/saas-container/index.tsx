import { cn } from "@/lib/utils";
import { ReactNode } from "react";

interface SaasContainerProps {
  containerClassName?: string;
  className?: string;
  children?: ReactNode;
}

export function SaasContainer({ containerClassName, className, children }: SaasContainerProps) {
  return (
    <div className={cn("max-lg:px-4 lg:pl-32 lg:pr-28 pb-24", containerClassName)}>
      <div className={cn("w-full lg:max-w-[912px] mx-auto", className)}>{children}</div>
    </div>
  );
}
