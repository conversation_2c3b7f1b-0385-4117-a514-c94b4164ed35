"use client";

import { cn } from "@/lib/utils";
import { Link } from "@/i18n/navigation";
import { usePathname } from "@/i18n/navigation";
import { AnchorHTMLAttributes } from "react";

interface SaasSidebarNavItemProps extends AnchorHTMLAttributes<HTMLAnchorElement> {
  href: string;
  title: string;
  icon: (props: any) => JSX.Element;
  active?: boolean;
  className?: string;
  exact?: boolean;
  noParent?: boolean;
}

export function SaasSidebarNavItem({
  href,
  title,
  icon: Icon,
  active,
  className,
  exact,
  noParent = false,
  ...props
}: SaasSidebarNavItemProps) {
  const pathname = usePathname();

  const isActive = href
    ? exact
      ? `/${pathname.split("/").slice(-2).join("/")}` === href
      : pathname.includes(href)
    : false;

  return (
    <Link
      aria-selected={isActive || active || false}
      href={href}
      data-active={isActive || active || false}
      data-parent={!noParent}
      className={cn(
        "group/nav-item px-6 py-3 flex items-center gap-4 cursor-pointer hover:bg-white border-l-[3px] data-[active=true]:bg-white border-white  data-[parent=false]:data-[active=true]:border-support-blue",
        className
      )}
      {...props}
    >
      <Icon
        width={24}
        height={24}
        className="fill-primary group-data-[active=true]/nav-item:fill-support-blue size-6"
      />
      <p className="text-primary group-data-[active=true]/nav-item:text-support-blue group-data-[active=true]/nav-item:font-bold group-hover/nav-item:text-support-blue text-md group-hover/nav-item:underline underline-offset-2">
        {title}
      </p>
    </Link>
  );
}
