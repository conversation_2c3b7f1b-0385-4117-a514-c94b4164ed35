"use client";

import { CountryIcon } from "@/components/_common/country-icon";
import { useCustomer } from "@/hooks/use-customer";
import { useLogout } from "@/hooks/use-logout";
import { useMediaQuery } from "@/hooks/use-media-query";
import { useSidebar } from "@/hooks/use-sidebar";
import { Link, usePathname, useRouter } from "@/i18n/navigation";
import { LizenzeroLogo } from "@arthursenno/lizenzero-ui-react/Figure";
import { Calculator, Logout, ShoppingBasket } from "@arthursenno/lizenzero-ui-react/Icon";
import { useSession } from "next-auth/react";
import { BiPlus, BiX } from "react-icons/bi";

import { SaasSidebarDivider } from "./saas-sidebar-divider";
import { SaasSidebarNavGroup } from "./saas-sidebar-nav-group";
import { SaasSidebarNavItem } from "./saas-sidebar-nav-item";
import { SidebarIcon } from "./sidebar-icon";
import { useTranslations } from "next-intl";

export function SaasSidebar() {
  const session = useSession();
  const router = useRouter();
  const pathname = usePathname();
  const isMobile = useMediaQuery("(max-width: 1024px)");
  const sidebar = useSidebar();

  const pagesT = useTranslations("modules.saas.pages");
  const globalT = useTranslations("modules.saas.global");

  const { customer } = useCustomer();
  const { logout } = useLogout();

  const euLicenseContract = customer?.contracts.find((contract) => contract.type === "EU_LICENSE");
  const directLicenseContract = customer?.contracts.find((contract) => contract.type === "DIRECT_LICENSE");
  const actionGuideContract = customer?.contracts.find((contract) => contract.type === "ACTION_GUIDE");

  const currentGroup = (() => {
    if (pathname.includes("direct-license")) return "direct-license";

    if (pathname.includes("eu-license")) return "eu-license";

    if (pathname.includes("action-guide")) return "action-guide";

    return "navigation";
  })();

  if (!session.data) return null;

  function handleNavigateToMyAccount() {
    sidebar.setOpen(false);
    router.push("/saas/my-account");
  }

  return (
    <div
      data-mobile={isMobile}
      data-show={!isMobile || sidebar.open}
      className="flex-none data-[show=false]:hidden fixed overflow-hidden max-lg:z-[9999] max-lg:h-full lg:relative w-full overflow-y-auto lg:w-[300px] border-r-[1px] border-tonal-dark-cream-80 bg-[#FCFCFC] z-50 pb-20"
    >
      <div className="w-full px-4 pt-6 pb-4 border-b-[1px] border-tonal-dark-cream-80 lg:hidden">
        <div className="h-10 w-full flex justify-between items-center">
          <LizenzeroLogo width={150} />
          <BiX
            onClick={() => sidebar.setOpen(false)}
            size={24}
            className="fill-primary hover:fill-grey-blue cursor-pointer"
          />
        </div>
      </div>
      <div className="h-3 w-full bg-tertiary hidden lg:block"></div>
      {/* Profile */}
      <div className="space-y-3 p-6 bg-white">
        <div className="space-y-1">
          <h1 className="text-xl font-bold text-tonal-dark-cream-10">
            {customer?.first_name} {customer?.last_name}
          </h1>
          <p className="text-sm text-tonal-dark-cream-30 w-full overflow-hidden text-ellipsis whitespace-nowrap">
            {customer?.email}
          </p>
        </div>
        <p className="font-bold text-tonal-dark-cream-30">{customer?.company?.name}</p>
        <p
          onClick={handleNavigateToMyAccount}
          className="text-md font-bold text-support-blue block hover:underline underline-offset-2"
        >
          {globalT("buttons.editProfile.label")}
        </p>
      </div>
      {/* Mobile Navigation */}
      <div className="border-b-[1px] border-t-[1px] border-tonal-dark-cream-90 lg:hidden">
        <SaasSidebarNavItem href="" title="Shopping Cart" icon={ShoppingBasket} className="font-bold" />
        <SaasSidebarDivider />
        <SaasSidebarNavItem href="" title="Log out" icon={Logout} className="font-bold" onClick={() => logout()} />
      </div>
      {/* My Services */}
      <div className="w-full">
        <div className="px-6 py-3 bg-white">
          <p className="text-md text-tonal-dark-cream-20">{globalT("words.myServices")}</p>
        </div>
        <div className="p-4">
          <Link
            href="/saas/license-assessment"
            className="px-6 py-4 bg-[#ebf4ff] hover:bg-[#d4e9ff] flex items-center gap-4 rounded-2xl transition-all duration-200"
          >
            <Calculator width={24} className="fill-primary" />
            <p className="text-primary text-md font-bold">{pagesT("general.licenseAssessment.sidebar")}</p>
          </Link>
        </div>
        {!!euLicenseContract && (
          <>
            <SaasSidebarDivider />
            <SaasSidebarNavGroup
              title={pagesT("euLicense.sidebarGroup")}
              icon={(props) => <SidebarIcon icon="eu-license" {...props} />}
              startOpen={currentGroup === "eu-license"}
            >
              <SaasSidebarNavItem
                id="sb-eu-dashboard"
                href="/saas/eu-license"
                exact
                title={pagesT("euLicense.dashboard.sidebar")}
                icon={(props) => <SidebarIcon icon="dashboard" {...props} />}
              />
              <SaasSidebarNavItem
                id="sb-eu-required-information"
                href="/saas/eu-license/required-information"
                title={pagesT("euLicense.requiredInformation.sidebar")}
                icon={(props) => <SidebarIcon icon="required-informations" {...props} />}
              />
              <SaasSidebarNavItem
                id="sb-eu-declare-volumes"
                href="/saas/eu-license/declare-volumes"
                title={pagesT("euLicense.declareVolumes.sidebar")}
                icon={(props) => <SidebarIcon icon="declare-volumes" {...props} />}
              />
              <SaasSidebarNavItem
                id="sb-eu-third-party-invoices"
                href="/saas/eu-license/third-party-invoices"
                title={pagesT("euLicense.thirdPartyInvoices.sidebar")}
                icon={(props) => <SidebarIcon icon="invoices" {...props} />}
              />
              <SaasSidebarNavItem
                id="sb-eu-certificates"
                href="/saas/eu-license/certificates"
                title={pagesT("euLicense.certificates.sidebar")}
                icon={(props) => <SidebarIcon icon="certificates" {...props} />}
              />
              <SaasSidebarNavGroup
                title={pagesT("euLicense.myCountries.sidebar")}
                icon={(props) => <SidebarIcon icon="my-countries" {...props} />}
              >
                {euLicenseContract.licenses.map((license) => (
                  <SaasSidebarNavItem
                    key={license.id}
                    href={`/saas/eu-license/countries/${license.country_code}`}
                    title={license.country_name}
                    icon={(props) => (
                      <CountryIcon
                        country={{ code: license.country_code, flag_url: license.country_flag }}
                        {...props}
                      />
                    )}
                  />
                ))}
              </SaasSidebarNavGroup>
            </SaasSidebarNavGroup>
          </>
        )}

        {!!directLicenseContract && (
          <>
            <SaasSidebarDivider />
            <SaasSidebarNavGroup
              title={pagesT("directLicense.sidebarGroup")}
              startOpen={currentGroup === "direct-license"}
              icon={(props) => <SidebarIcon icon="direct-license" {...props} />}
            >
              <SaasSidebarNavItem
                id="sb-de-dashboard"
                href="/saas/direct-license"
                exact
                title={pagesT("directLicense.dashboard.sidebar")}
                icon={(props) => <SidebarIcon icon="dashboard" {...props} />}
              />
              <SaasSidebarNavItem
                id="sb-de-lucid"
                href="/saas/direct-license/lucid"
                title={pagesT("directLicense.lucid.sidebar")}
                icon={(props) => <SidebarIcon icon="required-informations" {...props} />}
              />
              <SaasSidebarNavItem
                id="sb-de-declare-volumes"
                href="/saas/direct-license/declare-volumes"
                title={pagesT("directLicense.declareVolumes.sidebar")}
                icon={(props) => <SidebarIcon icon="declare-volumes" {...props} />}
              />
              <SaasSidebarNavItem
                id="sb-de-certificates"
                href="/saas/direct-license/certificates"
                title={pagesT("directLicense.certificates.sidebar")}
                icon={(props) => <SidebarIcon icon="certificates" {...props} />}
              />
              <SaasSidebarNavItem
                id="sb-de-sustainability"
                href="/saas/direct-license/sustainability"
                title={pagesT("directLicense.sustainability.sidebar")}
                icon={(props) => <SidebarIcon icon="sustainability" {...props} />}
              />
            </SaasSidebarNavGroup>
          </>
        )}

        {!!actionGuideContract && (
          <>
            <SaasSidebarDivider />
            <SaasSidebarNavGroup
              title={pagesT("actionGuide.sidebarGroup")}
              icon={(props) => <SidebarIcon icon="action-guide" {...props} />}
              startOpen={currentGroup === "action-guide"}
            >
              <SaasSidebarNavItem
                id="sb-action-dashboard"
                href="/saas/action-guide"
                exact
                title={pagesT("actionGuide.dashboard.sidebar")}
                icon={(props) => <SidebarIcon icon="dashboard" {...props} />}
              />
              <SaasSidebarNavGroup
                id="sb-action-countries"
                title={pagesT("actionGuide.myCountries.sidebar")}
                icon={(props) => <SidebarIcon icon="my-countries" {...props} />}
              >
                {actionGuideContract.action_guides.map((actionGuide) => (
                  <SaasSidebarNavItem
                    key={actionGuide.id}
                    href={`/saas/action-guide/countries/${actionGuide.country_code}`}
                    title={actionGuide.country_name}
                    icon={(props) => (
                      <CountryIcon
                        country={{ code: actionGuide.country_code, flag_url: actionGuide.country_flag }}
                        {...props}
                      />
                    )}
                  />
                ))}
              </SaasSidebarNavGroup>
            </SaasSidebarNavGroup>
          </>
        )}
      </div>
      {/* Additional Services */}
      <div className="w-full px-6 py-4 border-t-[1px] border-b-[1px] border-tonal-dark-cream-90">
        <Link
          href="/saas/add-services"
          className="w-full flex items-center gap-4 bg-tertiary hover:bg-tertiary/80 cursor-pointer rounded-2xl py-3 px-1 justify-center"
        >
          <BiPlus size={20} className="text-tonal-dark-cream-10" />
          <p className="text-tonal-dark-cream-10 font-bold text-md">{pagesT("general.additionalServices.sidebar")}</p>
        </Link>
      </div>
      {/* Navigation */}
      <div className="w-full space-y-3 py-4">
        <div className="px-6 py-3">
          <p className="text-md text-tonal-dark-cream-20">{pagesT("general.sidebarGroup")}</p>
        </div>
        <div>
          <SaasSidebarNavItem
            href="/saas/my-account"
            title={pagesT("general.myAccount.sidebar")}
            icon={(props) => <SidebarIcon icon="my-account" {...props} />}
            noParent
          />
          <SaasSidebarNavItem
            id="sb-eu-contract-management"
            href="/saas/contract-management"
            title={pagesT("general.contractManagement.sidebar")}
            icon={(props) => <SidebarIcon icon="contracts" {...props} />}
            noParent
          />
          <SaasSidebarNavItem
            id="sb-eu-invoices-payment"
            href="/saas/invoices-and-payment"
            title={pagesT("general.invoicesAndPayment.sidebar")}
            icon={(props) => <SidebarIcon icon="invoices" {...props} />}
            noParent
          />
          <SaasSidebarDivider />
          <SaasSidebarNavItem
            href="/saas/partner-deals"
            title={pagesT("general.partnerDeals.sidebar")}
            icon={(props) => <SidebarIcon icon="partner-deals" {...props} />}
            noParent
          />
          <SaasSidebarNavItem
            href="/saas/invite-customers"
            title={pagesT("general.inviteCustomers.sidebar")}
            icon={(props) => <SidebarIcon icon="invite-customers" {...props} />}
          />
        </div>
      </div>
      {/* Need any help? */}
      <div className="w-full">
        <div className="p-4">
          <div className="px-6 py-4 bg-surface-04 flex items-center gap-4 rounded-2xl">
            <Calculator width={24} className="fill-on-surface-04" />
            <p className="text-on-surface-04 text-md font-bold">Need any help?</p>
          </div>
        </div>
      </div>
    </div>
  );
}
