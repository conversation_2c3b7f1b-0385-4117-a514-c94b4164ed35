import { ComponentProps } from "react";

const SIDEBAR_ICONS = {
  "eu-license": (props: ComponentProps<"svg">) => (
    <svg width="24" height="24" {...props} viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
      <path d="M12 0C5.37258 0 0 5.37258 0 12C0 18.6274 5.37258 24 12 24C18.6274 24 24 18.6274 24 12C24 5.37258 18.6274 0 12 0Z" />
      <mask
        id="mask0_14057_347604"
        style={{ maskType: "luminance" }}
        maskUnits="userSpaceOnUse"
        x="2"
        y="2"
        width="20"
        height="20"
      >
        <path d="M2.44412 21.334L21.333 21.334L21.333 2.4451L2.44412 2.4451L2.44412 21.334Z" fill="white" />
      </mask>
      <g mask="url(#mask0_14057_347604)">
        <path
          d="M12.8263 19.6454C12.747 19.5927 12.7208 19.4871 12.747 19.408L12.8792 18.854C12.9321 18.6429 12.6943 18.4847 12.5094 18.5903L12.0339 18.8803C11.9545 18.9331 11.849 18.9331 11.7696 18.8803L11.2941 18.5903C11.1092 18.4847 10.8714 18.6429 10.9243 18.854L11.0563 19.408C11.0828 19.4871 11.0563 19.5927 10.977 19.6454L10.5543 20.0147C10.3959 20.1467 10.4752 20.4105 10.6865 20.4369L11.2412 20.4896C11.3205 20.4896 11.3999 20.5425 11.4525 20.6478L11.6639 21.1756C11.7432 21.3865 12.0339 21.3865 12.113 21.1756L12.3243 20.6478C12.3508 20.5687 12.4301 20.516 12.5356 20.4896L13.0905 20.4369C13.3019 20.4105 13.3812 20.1467 13.2225 20.0147L12.8259 19.6454H12.8263Z"
          fill="white"
        />
        <path
          d="M17.1325 18.2469L16.5776 18.1415C16.4985 18.1151 16.4192 18.0623 16.3927 17.9832L16.2343 17.4555C16.1814 17.2445 15.8909 17.2181 15.7852 17.4028L15.5209 17.904C15.468 17.9832 15.3889 18.0359 15.3096 18.0359L14.7549 18.0095C14.5436 18.0095 14.4378 18.2734 14.57 18.4052L14.9663 18.8274C15.0192 18.8801 15.0454 18.9856 15.0192 19.0647L14.834 19.5925C14.7549 19.8034 14.9663 19.9881 15.1776 19.8827L15.6796 19.6452C15.7587 19.6187 15.8645 19.6187 15.9172 19.6716L16.3663 20.0145C16.5249 20.1465 16.7892 20.0145 16.7627 19.8034L16.6834 19.2494C16.6834 19.1703 16.7098 19.0647 16.7892 19.0121L17.2645 18.6954C17.3976 18.5636 17.3447 18.2992 17.1329 18.2469H17.1325Z"
          fill="white"
        />
        <path
          d="M9.07544 18.0095L8.52068 18.0359C8.44141 18.0359 8.33575 17.9832 8.30933 17.904L8.04515 17.4028C7.93948 17.2181 7.6753 17.2445 7.59604 17.4555L7.43755 17.9832C7.41113 18.0623 7.33186 18.1415 7.25261 18.1415L6.69784 18.2469C6.48648 18.2998 6.43366 18.5636 6.59217 18.6954L7.04175 19.0121C7.12099 19.0647 7.14742 19.1438 7.14742 19.2494L7.06815 19.8034C7.04175 20.0145 7.2795 20.1465 7.46444 20.0145L7.91352 19.6716C7.99279 19.6187 8.07204 19.6187 8.17773 19.6452L8.67966 19.8827C8.86455 19.9618 9.07588 19.7772 9.02299 19.5925L8.8381 19.0647C8.81166 18.9856 8.8381 18.8801 8.89099 18.8274L9.26077 18.4052C9.39299 18.2469 9.28722 17.9832 9.07588 18.0095H9.07544Z"
          fill="white"
        />
        <path
          d="M6.38018 15.7678L5.87824 15.504C5.79898 15.4512 5.74616 15.3721 5.74616 15.2929V14.7389C5.74616 14.5279 5.48198 14.4224 5.34989 14.5543L4.9536 14.95C4.87436 15.0027 4.79511 15.0291 4.71585 15.0027L4.18749 14.8181C3.97613 14.7389 3.79122 14.95 3.89689 15.161L4.13464 15.6623C4.16107 15.7414 4.16107 15.8469 4.10825 15.8997L3.7648 16.3482C3.63271 16.5065 3.7648 16.7703 3.97613 16.7439L4.53093 16.6389C4.61018 16.6389 4.71585 16.6652 4.76869 16.7444L5.08571 17.2192C5.2178 17.4039 5.48198 17.3248 5.5348 17.1137L5.64049 16.5597C5.66689 16.4806 5.71973 16.4014 5.79898 16.375L6.32736 16.2167C6.53869 16.164 6.56511 15.8738 6.38018 15.7683V15.7678Z"
          fill="white"
        />
        <path
          d="M19.6954 15.8723C19.6425 15.7932 19.6425 15.714 19.669 15.6349L19.9068 15.1336C19.9859 14.949 19.801 14.7379 19.6161 14.7907L19.0877 14.9754C19.0085 15.0017 18.9028 14.9754 18.8499 14.9226L18.4537 14.5269C18.2952 14.3686 18.031 14.5005 18.0574 14.7115L18.0839 15.2655C18.0839 15.3447 18.031 15.4502 17.9517 15.4766L17.4499 15.7404C17.265 15.8459 17.2912 16.1097 17.5028 16.1889L18.031 16.3472C18.1103 16.3736 18.1894 16.4527 18.1894 16.5318L18.2952 17.0858C18.3481 17.2969 18.6123 17.3496 18.7443 17.1914L19.035 16.7424C19.0877 16.6633 19.167 16.6369 19.2728 16.6369L19.8274 16.716C20.0388 16.7424 20.171 16.505 20.0388 16.3203L19.6954 15.8718V15.8723Z"
          fill="white"
        />
        <path
          d="M20.646 11.4415C20.5667 11.4151 20.514 11.3359 20.4876 11.2304L20.4347 10.6764C20.4082 10.4654 20.144 10.3862 20.012 10.5445L19.6422 10.9666C19.5893 11.0457 19.4836 11.0721 19.4045 11.0458L18.8496 10.9138C18.6382 10.8611 18.4798 11.0985 18.5853 11.2832L18.876 11.758C18.9289 11.8372 18.9289 11.9427 18.876 12.0218L18.5853 12.4967C18.4798 12.6814 18.6382 12.9188 18.8496 12.866L19.4045 12.7341C19.4836 12.7078 19.5893 12.7341 19.6422 12.8133L20.012 13.2354C20.144 13.3937 20.4082 13.3145 20.4347 13.1035L20.4876 12.5495C20.4876 12.4703 20.5404 12.3912 20.646 12.3384L21.1745 12.1274C21.3858 12.0482 21.3858 11.758 21.1745 11.6789L20.646 11.4419V11.4415Z"
          fill="white"
        />
        <path
          d="M4.13508 12.8133C4.18793 12.7341 4.29359 12.7078 4.37286 12.7341L4.92764 12.8661C5.13897 12.9188 5.29748 12.6814 5.19182 12.4967L4.90121 12.0219C4.84837 11.9427 4.84837 11.8372 4.90121 11.758L5.19182 11.2832C5.29748 11.0985 5.13897 10.8611 4.92764 10.9138L4.37286 11.0457C4.29359 11.0721 4.18793 11.0457 4.13508 10.9666L3.76524 10.5445C3.63315 10.3862 3.36897 10.4654 3.34255 10.6764L3.2897 11.2304C3.2897 11.3096 3.23688 11.3887 3.13119 11.4415L2.60284 11.6525C2.3915 11.7317 2.3915 12.0219 2.60284 12.101L3.13119 12.312C3.21046 12.3384 3.26328 12.4176 3.2897 12.5231L3.34255 13.0771C3.36897 13.2881 3.63315 13.3673 3.76524 13.209L4.13508 12.8128V12.8133Z"
          fill="white"
        />
        <path
          d="M4.10825 7.90718C4.16107 7.98633 4.16107 8.06547 4.13464 8.14462L3.89689 8.64584C3.81764 8.83053 4.00256 9.04158 4.18749 8.9888L4.71585 8.80413C4.79511 8.77776 4.90078 8.80413 4.9536 8.85691L5.37631 9.25262C5.5348 9.41091 5.79898 9.279 5.77258 9.06796L5.74616 8.51396C5.74616 8.4348 5.79898 8.32929 5.87824 8.30289L6.38018 8.03909C6.56511 7.93356 6.53869 7.66976 6.32736 7.5906L5.79898 7.43233C5.71973 7.40593 5.64049 7.3268 5.64049 7.24764L5.5348 6.69364C5.5084 6.4826 5.2178 6.42984 5.08571 6.58813L4.76869 7.06298C4.71585 7.14213 4.6366 7.16851 4.53093 7.16851L3.97613 7.08938C3.7648 7.06298 3.63271 7.30042 3.7648 7.48509L4.10825 7.90764V7.90718Z"
          fill="white"
        />
        <path
          d="M17.4232 8.0125L17.925 8.2763C18.0043 8.32908 18.0572 8.40822 18.0572 8.48737L18.0308 9.04137C18.0308 9.25242 18.295 9.35795 18.427 9.22604L18.8232 8.83033C18.8761 8.77755 18.9819 8.75117 19.061 8.77755L19.5894 8.96222C19.8008 9.04137 19.9857 8.83033 19.8801 8.61926L19.6423 8.11804C19.6159 8.03888 19.6159 7.93335 19.6688 7.88059L20.0121 7.43213C20.1441 7.27384 20.0121 7.01002 19.8008 7.03639L19.2459 7.11555C19.1668 7.11555 19.061 7.08917 19.0081 7.01002L18.6912 6.53515C18.559 6.35048 18.295 6.42964 18.2421 6.64068L18.1363 7.19468C18.1101 7.27384 18.0572 7.35297 17.9779 7.37935L17.4494 7.53764C17.2645 7.6163 17.2381 7.9065 17.4232 8.01204V8.0125Z"
          fill="white"
        />
        <path
          d="M14.7282 5.7692L15.2828 5.74282C15.3622 5.74282 15.4677 5.79558 15.4942 5.87471L15.7584 6.37595C15.8639 6.56062 16.1546 6.53424 16.2075 6.3232L16.3659 5.79557C16.3924 5.71642 16.4717 5.63729 16.5508 5.63729L17.1057 5.53175C17.317 5.479 17.3699 5.2152 17.2113 5.08329L16.7359 4.76671C16.6566 4.71395 16.6302 4.6348 16.6302 4.52929L16.7353 3.9748C16.7617 3.76375 16.5239 3.63184 16.339 3.76375L15.8899 4.10671C15.8108 4.15946 15.7315 4.15946 15.6522 4.13309L15.1504 3.89566C14.9653 3.81651 14.7539 4.00118 14.8068 4.18584L14.9917 4.71346C15.0182 4.79262 14.9917 4.89815 14.939 4.95091L14.5426 5.373C14.4106 5.53129 14.5162 5.79511 14.7275 5.76873L14.7282 5.7692Z"
          fill="white"
        />
        <path
          d="M8.86368 4.9509C8.8108 4.89814 8.78435 4.79261 8.8108 4.71348L8.99568 4.18585C9.07502 3.97479 8.86368 3.79012 8.65233 3.89565L8.15039 4.13308C8.07113 4.15948 7.96546 4.15948 7.88622 4.1067L7.43711 3.76374C7.27859 3.63185 7.01442 3.76374 7.04084 3.97479L7.14651 4.52881C7.14651 4.60794 7.12008 4.71348 7.04084 4.76623L6.56531 5.08281C6.38037 5.21472 6.43322 5.47852 6.67097 5.53128L7.22575 5.63681C7.30502 5.66319 7.38426 5.71594 7.41068 5.7951L7.56919 6.32272C7.62202 6.53377 7.91262 6.56014 8.01831 6.37548L8.28248 5.87423C8.33531 5.7951 8.41457 5.74234 8.49382 5.74234L9.04857 5.76872C9.25991 5.76872 9.36568 5.5049 9.23346 5.37299L8.86324 4.9509H8.86368Z"
          fill="white"
        />
        <path
          d="M13.1168 3.34226L12.5622 3.28951C12.4828 3.28951 12.4037 3.23675 12.3508 3.13122L12.1395 2.6036C12.0602 2.39255 11.7695 2.39255 11.6904 2.6036L11.4791 3.13122C11.4526 3.21037 11.3733 3.26313 11.2677 3.28951L10.7128 3.34226C10.5015 3.36866 10.4222 3.63246 10.5808 3.76437L11.0035 4.13371C11.0826 4.18646 11.1091 4.292 11.0826 4.37113L10.9506 4.92513C10.8977 5.1362 11.1355 5.29448 11.3204 5.18895L11.796 4.89875C11.8753 4.846 11.9808 4.846 12.0602 4.89875L12.5357 5.18895C12.7206 5.29448 12.9584 5.1362 12.9055 4.92513L12.7735 4.37113C12.7471 4.292 12.7735 4.18646 12.8528 4.13371L13.2755 3.76437C13.408 3.63246 13.3282 3.36866 13.1173 3.34226H13.1168Z"
          fill="white"
        />
      </g>
    </svg>
  ),
  "direct-license": (props: ComponentProps<"svg">) => (
    <svg width="20" height="20" {...props} viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
      <path d="M7.32307 4.22183C5.28307 6.51183 3.88307 9.80183 3.75307 10.1118L1.49307 9.14183C0.843073 8.86183 0.683073 8.01183 1.18307 7.51183L4.19307 4.50183C4.66307 4.03183 5.34307 3.82183 6.00307 3.95183L7.32307 4.22183ZM8.81307 14.3818C9.11307 14.6818 9.55307 14.7618 9.93307 14.5818C11.0931 14.0418 13.5831 12.7718 15.1931 11.1618C19.7831 6.57183 19.8231 2.83183 19.5531 1.23183C19.4831 0.831831 19.1631 0.511831 18.7631 0.441831C17.1631 0.171831 13.4231 0.211831 8.83307 4.80183C7.22307 6.41183 5.96307 8.90183 5.41307 10.0618C5.23307 10.4418 5.32307 10.8918 5.61307 11.1818L8.81307 14.3818ZM15.7831 12.6818C13.4931 14.7218 10.2031 16.1218 9.89307 16.2518L10.8631 18.5118C11.1431 19.1618 11.9931 19.3218 12.4931 18.8218L15.5031 15.8118C15.9731 15.3418 16.1831 14.6618 16.0531 14.0018L15.7831 12.6818ZM7.07307 15.2818C7.27307 16.3418 6.92307 17.3218 6.25307 17.9918C5.48307 18.7618 3.09307 19.3318 1.54307 19.6318C0.853073 19.7618 0.243073 19.1518 0.373073 18.4618C0.673073 16.9118 1.23307 14.5218 2.01307 13.7518C2.68307 13.0818 3.66307 12.7318 4.72307 12.9318C5.89307 13.1518 6.85307 14.1118 7.07307 15.2818ZM11.1331 6.87183C11.1331 5.77183 12.0331 4.87183 13.1331 4.87183C14.2331 4.87183 15.1331 5.77183 15.1331 6.87183C15.1331 7.97183 14.2331 8.87183 13.1331 8.87183C12.0331 8.87183 11.1331 7.97183 11.1331 6.87183Z" />
    </svg>
  ),
  "action-guide": (props: ComponentProps<"svg">) => (
    <svg width="22" height="18" {...props} viewBox="0 0 22 18" xmlns="http://www.w3.org/2000/svg">
      <path d="M16.5049 0.677734C14.5549 0.677734 12.4549 1.07773 11.0049 2.17773C9.55488 1.07773 7.45488 0.677734 5.50488 0.677734C4.05488 0.677734 2.51488 0.897734 1.22488 1.46773C0.494883 1.79773 0.00488281 2.50773 0.00488281 3.31773V14.5977C0.00488281 15.8977 1.22488 16.8577 2.48488 16.5377C3.46488 16.2877 4.50488 16.1777 5.50488 16.1777C7.06488 16.1777 8.72488 16.4377 10.0649 17.0977C10.6649 17.3977 11.3449 17.3977 11.9349 17.0977C13.2749 16.4277 14.9349 16.1777 16.4949 16.1777C17.4949 16.1777 18.5349 16.2877 19.5149 16.5377C20.7749 16.8677 21.9949 15.9077 21.9949 14.5977V3.31773C21.9949 2.50773 21.5049 1.79773 20.7749 1.46773C19.4949 0.897734 17.9549 0.677734 16.5049 0.677734ZM20.0049 13.4077C20.0049 14.0377 19.4249 14.4977 18.8049 14.3877C18.0549 14.2477 17.2749 14.1877 16.5049 14.1877C14.8049 14.1877 12.3549 14.8377 11.0049 15.6877V4.17773C12.3549 3.32773 14.8049 2.67773 16.5049 2.67773C17.4249 2.67773 18.3349 2.76773 19.2049 2.95773C19.6649 3.05773 20.0049 3.46773 20.0049 3.93773V13.4077Z" />
      <path d="M12.9849 7.18773C12.6649 7.18773 12.3749 6.98773 12.2749 6.66773C12.1449 6.27773 12.3649 5.84773 12.7549 5.72773C14.2949 5.22773 16.2849 5.06773 18.1149 5.27773C18.5249 5.32773 18.8249 5.69773 18.7749 6.10773C18.7249 6.51773 18.3549 6.81773 17.9449 6.76773C16.3249 6.57773 14.5549 6.72773 13.2149 7.15773C13.1349 7.16773 13.0549 7.18773 12.9849 7.18773Z" />
      <path d="M12.9849 9.84773C12.6649 9.84773 12.3749 9.64773 12.2749 9.32773C12.1449 8.93773 12.3649 8.50773 12.7549 8.38773C14.2849 7.88773 16.2849 7.72773 18.1149 7.93773C18.5249 7.98773 18.8249 8.35773 18.7749 8.76773C18.7249 9.17773 18.3549 9.47773 17.9449 9.42773C16.3249 9.23773 14.5549 9.38773 13.2149 9.81773C13.1349 9.83774 13.0549 9.84773 12.9849 9.84773Z" />
      <path d="M12.9849 12.5077C12.6649 12.5077 12.3749 12.3077 12.2749 11.9877C12.1449 11.5977 12.3649 11.1677 12.7549 11.0477C14.2849 10.5477 16.2849 10.3877 18.1149 10.5977C18.5249 10.6477 18.8249 11.0177 18.7749 11.4277C18.7249 11.8377 18.3549 12.1277 17.9449 12.0877C16.3249 11.8977 14.5549 12.0477 13.2149 12.4777C13.1349 12.4977 13.0549 12.5077 12.9849 12.5077Z" />
    </svg>
  ),
  certificates: (props: ComponentProps<"svg">) => (
    <svg width="16" height="22" {...props} viewBox="0 0 16 22" xmlns="http://www.w3.org/2000/svg">
      <path d="M6.92 11.4429L8 10.6229L9.07 11.4329C9.46 11.7229 9.99 11.3529 9.85 10.8829L9.43 9.52287L10.63 8.57287C11 8.29287 10.79 7.69287 10.31 7.69287H8.91L8.48 6.35287C8.33 5.89287 7.68 5.89287 7.53 6.35287L7.09 7.69287H5.68C5.21 7.69287 5 8.29287 5.37 8.58287L6.56 9.53287L6.14 10.8929C6 11.3629 6.53 11.7329 6.92 11.4429ZM2 20.3029C2 20.9829 2.67 21.4629 3.32 21.2529L8 19.6929L12.68 21.2529C13.33 21.4729 14 20.9929 14 20.3029V13.9729C15.24 12.5629 16 10.7229 16 8.69287C16 4.27287 12.42 0.692871 8 0.692871C3.58 0.692871 0 4.27287 0 8.69287C0 10.7229 0.76 12.5629 2 13.9729V20.3029ZM8 2.69287C11.31 2.69287 14 5.38287 14 8.69287C14 12.0029 11.31 14.6929 8 14.6929C4.69 14.6929 2 12.0029 2 8.69287C2 5.38287 4.69 2.69287 8 2.69287Z" />
    </svg>
  ),
  contracts: (props: ComponentProps<"svg">) => (
    <svg width="18" height="16" {...props} viewBox="0 0 18 16" xmlns="http://www.w3.org/2000/svg">
      <path d="M12 12.0015L8 16.0015H16C17.1 16.0015 18 15.1015 18 14.0015C18 12.9015 17.1 12.0015 16 12.0015H12Z" />
      <path d="M9.06 3.19152L0.29 11.9615C0.11 12.1415 0 12.4015 0 12.6615V15.0015C0 15.5515 0.45 16.0015 1 16.0015H3.34C3.61 16.0015 3.86 15.8915 4.05 15.7115L12.82 6.94152L9.06 3.19152Z" />
      <path d="M15.71 4.04152C16.1 3.65152 16.1 3.02152 15.71 2.63152L13.37 0.291523C12.98 -0.0984766 12.35 -0.0984766 11.96 0.291523L10.13 2.12152L13.88 5.87152L15.71 4.04152Z" />
    </svg>
  ),
  invoices: (props: ComponentProps<"svg">) => (
    <svg width="18" height="20" {...props} viewBox="0 0 18 20" xmlns="http://www.w3.org/2000/svg">
      <path d="M18 0.210195C17.87 0.210195 17.74 0.260195 17.65 0.360195L16.86 1.1502C16.66 1.3502 16.35 1.3502 16.15 1.1502L15.36 0.360195C15.16 0.160195 14.85 0.160195 14.65 0.360195L13.86 1.1502C13.66 1.3502 13.35 1.3502 13.15 1.1502L12.36 0.360195C12.16 0.160195 11.85 0.160195 11.65 0.360195L10.86 1.1502C10.66 1.3502 10.35 1.3502 10.15 1.1502L9.36 0.360195C9.16 0.160195 8.85 0.160195 8.65 0.360195L7.86 1.1502C7.66 1.3502 7.35 1.3502 7.15 1.1502L6.35 0.350195C6.15 0.150195 5.84 0.150195 5.64 0.350195L4.85 1.1502C4.65 1.3502 4.34 1.3502 4.14 1.1502L3.35 0.350195C3.15 0.150195 2.84 0.150195 2.64 0.350195L1.85 1.1502C1.65 1.3502 1.34 1.3502 1.14 1.1502L0.35 0.350195C0.26 0.260195 0.13 0.210195 0 0.210195V19.8002C0.13 19.8002 0.26 19.7502 0.35 19.6502L1.14 18.8602C1.34 18.6602 1.65 18.6602 1.85 18.8602L2.64 19.6502C2.84 19.8502 3.15 19.8502 3.35 19.6502L4.14 18.8602C4.34 18.6602 4.65 18.6602 4.85 18.8602L5.64 19.6502C5.84 19.8502 6.15 19.8502 6.35 19.6502L7.14 18.8602C7.34 18.6602 7.65 18.6602 7.85 18.8602L8.64 19.6502C8.84 19.8502 9.15 19.8502 9.35 19.6502L10.14 18.8602C10.34 18.6602 10.65 18.6602 10.85 18.8602L11.64 19.6502C11.84 19.8502 12.15 19.8502 12.35 19.6502L13.14 18.8602C13.34 18.6602 13.65 18.6602 13.85 18.8602L14.64 19.6502C14.84 19.8502 15.15 19.8502 15.35 19.6502L16.14 18.8602C16.34 18.6602 16.65 18.6602 16.85 18.8602L17.64 19.6502C17.74 19.7502 17.87 19.8002 17.99 19.8002V0.210195H18ZM14 15.0002H4C3.45 15.0002 3 14.5502 3 14.0002C3 13.4502 3.45 13.0002 4 13.0002H14C14.55 13.0002 15 13.4502 15 14.0002C15 14.5502 14.55 15.0002 14 15.0002ZM14 11.0002H4C3.45 11.0002 3 10.5502 3 10.0002C3 9.4502 3.45 9.0002 4 9.0002H14C14.55 9.0002 15 9.4502 15 10.0002C15 10.5502 14.55 11.0002 14 11.0002ZM14 7.0002H4C3.45 7.0002 3 6.5502 3 6.0002C3 5.4502 3.45 5.0002 4 5.0002H14C14.55 5.0002 15 5.4502 15 6.0002C15 6.5502 14.55 7.0002 14 7.0002Z" />
    </svg>
  ),
  "partner-deals": (props: ComponentProps<"svg">) => (
    <svg width="20" height="18" {...props} viewBox="0 0 20 18" xmlns="http://www.w3.org/2000/svg">
      <path d="M14.4799 7.41265C14.0899 7.80265 13.4399 7.80265 13.0499 7.41265L8.57993 2.95265L1.52993 9.99265L0.869932 9.36265C-0.300068 8.19265 -0.300068 6.29265 0.869932 5.12265L5.10993 0.882648C6.27993 -0.287352 8.17993 -0.287352 9.34993 0.882648L14.4799 6.00265C14.8699 6.39265 14.8699 7.02265 14.4799 7.41265ZM15.1799 5.29265C15.9599 6.07265 15.9599 7.34265 15.1799 8.12265C13.9099 9.39265 12.5699 8.34265 12.3499 8.12265L8.58993 4.36265L3.01993 9.93265C2.62993 10.3226 2.62993 10.9526 3.01993 11.3426C3.40993 11.7326 4.03993 11.7326 4.43993 11.3426L9.05993 6.72265L9.76993 7.43265L5.14993 12.0526C4.75993 12.4426 4.75993 13.0726 5.14993 13.4626C5.53993 13.8526 6.16993 13.8526 6.56993 13.4626L11.1899 8.84265L11.8999 9.55265L7.27993 14.1726C6.88993 14.5626 6.88993 15.1926 7.27993 15.5826C7.66993 15.9726 8.29993 15.9726 8.68993 15.5826L13.3099 10.9626L14.0199 11.6726L9.39993 16.2926C9.00993 16.6826 9.00993 17.3126 9.39993 17.7026C9.78993 18.0926 10.4199 18.0926 10.8099 17.7026L19.1299 9.36265C20.2999 8.19265 20.2999 6.29265 19.1299 5.12265L14.8899 0.882648C13.7399 -0.267352 11.8799 -0.287352 10.7099 0.822648L15.1799 5.29265Z" />
    </svg>
  ),
  "invite-customers": (props: ComponentProps<"svg">) => (
    <svg width="22" height="16" {...props} viewBox="0 0 22 16" xmlns="http://www.w3.org/2000/svg">
      <path d="M14.39 10.56C12.71 9.7 10.53 9 8 9C5.47 9 3.29 9.7 1.61 10.56C0.61 11.07 0 12.1 0 13.22V16H16V13.22C16 12.1 15.39 11.07 14.39 10.56ZM8 8C10.21 8 12 6.21 12 4C12 1.79 10.21 0 8 0C5.79 0 4 1.79 4 4C4 6.21 5.79 8 8 8ZM19 5V3C19 2.45 18.55 2 18 2C17.45 2 17 2.45 17 3V5H15C14.45 5 14 5.45 14 6C14 6.55 14.45 7 15 7H17V9C17 9.55 17.45 10 18 10C18.55 10 19 9.55 19 9V7H21C21.55 7 22 6.55 22 6C22 5.45 21.55 5 21 5H19Z" />
    </svg>
  ),
  "my-account": (props: ComponentProps<"svg">) => (
    <svg width="14" height="24" {...props} viewBox="0 0 14 24" xmlns="http://www.w3.org/2000/svg">
      <path d="M11.8631 11.8581C11.2893 11.2506 10.3827 11.061 9.61834 11.4036C8.78055 11.7782 7.85325 11.986 6.87775 11.986C5.90224 11.986 4.97264 11.7782 4.13715 11.4036C3.37282 11.061 2.46618 11.2506 1.89235 11.8581C0.744698 13.0754 0.0400391 14.713 0.0400391 16.5127V21.7132C0.0400391 22.6382 0.714858 23.4284 1.63298 23.5769C5.10807 24.141 8.65201 24.141 12.1271 23.5769C13.0452 23.4284 13.72 22.6382 13.72 21.7132V16.5127C13.72 14.7107 13.0154 13.0754 11.8677 11.8581H11.8631Z" />
      <path d="M6.87775 10.6157C9.82379 10.6157 12.212 8.23926 12.212 5.30783C12.212 2.3764 9.82379 0 6.87775 0C3.9317 0 1.54346 2.3764 1.54346 5.30783C1.54346 8.23926 3.9317 10.6157 6.87775 10.6157Z" />
    </svg>
  ),
  "my-countries": (props: ComponentProps<"svg">) => (
    <svg width="16" height="18" {...props} viewBox="0 0 16 18" xmlns="http://www.w3.org/2000/svg">
      <path d="M9.9 2.5L9.66 1.3C9.57 0.84 9.16 0.5 8.68 0.5H1.5C0.95 0.5 0.5 0.95 0.5 1.5V16.5C0.5 17.05 0.95 17.5 1.5 17.5C2.05 17.5 2.5 17.05 2.5 16.5V10.5H8.1L8.34 11.7C8.43 12.17 8.84 12.5 9.32 12.5H14.5C15.05 12.5 15.5 12.05 15.5 11.5V3.5C15.5 2.95 15.05 2.5 14.5 2.5H9.9Z" />
    </svg>
  ),
  "required-informations": (props: ComponentProps<"svg">) => (
    <svg width="16" height="20" {...props} viewBox="0 0 16 20" xmlns="http://www.w3.org/2000/svg">
      <path d="M10.59 0.59C10.21 0.21 9.7 0 9.17 0H2C0.9 0 0 0.9 0 2V18C0 19.1 0.89 20 1.99 20H14C15.1 20 16 19.1 16 18V6.83C16 6.3 15.79 5.79 15.41 5.42L10.59 0.59ZM11 16H5C4.45 16 4 15.55 4 15C4 14.45 4.45 14 5 14H11C11.55 14 12 14.45 12 15C12 15.55 11.55 16 11 16ZM11 12H5C4.45 12 4 11.55 4 11C4 10.45 4.45 10 5 10H11C11.55 10 12 10.45 12 11C12 11.55 11.55 12 11 12ZM9 6V1.5L14.5 7H10C9.45 7 9 6.55 9 6Z" />
    </svg>
  ),
  sustainability: (props: ComponentProps<"svg">) => (
    <svg width="24" height="24" {...props} viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
      <path d="M7.68317 18.6698H3.71304C3.42476 18.6698 3.2781 18.4866 3.21741 18.3797C3.15672 18.2728 3.0758 18.0489 3.22752 17.8046L4.85097 15.1429L6.37328 15.9674C6.69696 16.1455 7.07122 15.835 6.95995 15.4788L5.58431 11.1019C5.51857 10.8882 5.29604 10.7711 5.08363 10.8271L0.688668 12.0689C0.334644 12.1707 0.279005 12.6542 0.602684 12.8272L2.08959 13.6364L0.557168 16.1455C-0.155938 17.3109 -0.186284 18.7207 0.476247 19.9167C1.13878 21.1127 2.34752 21.8252 3.70799 21.8252H7.67811C8.54294 21.8252 9.24593 21.1178 9.24593 20.2475C9.24593 19.3772 8.54294 18.6698 7.67811 18.6698H7.68317Z" />
      <path d="M17.5554 8.84733L18.7237 4.4094C18.8198 4.05315 18.4303 3.75796 18.1168 3.95136L16.6703 4.82673L15.2997 2.22097C14.6625 1.0097 13.474 0.271747 12.1135 0.241211C10.7531 0.210675 9.52916 0.897739 8.84134 2.07338L6.82845 5.51379C6.38845 6.26702 6.63627 7.234 7.38478 7.67168C7.6326 7.81927 7.91076 7.89053 8.17881 7.89053C8.7149 7.89053 9.24088 7.61061 9.52915 7.11185L11.542 3.67144C11.6887 3.42206 11.9163 3.39153 12.0427 3.39153C12.1641 3.39153 12.3968 3.43733 12.5283 3.6918L13.9798 6.45023L12.503 7.35105C12.1894 7.54445 12.2653 8.02285 12.6243 8.10428L17.0699 9.13233C17.2873 9.18323 17.4998 9.0509 17.5554 8.83715V8.84733Z" />
      <path d="M23.4322 16.1557L21.3283 12.7661C20.8681 12.0282 19.9021 11.7992 19.1687 12.2623C18.4354 12.7254 18.2078 13.6975 18.668 14.4355L20.772 17.825C20.9237 18.0693 20.8478 18.2932 20.7871 18.4001C20.7264 18.5069 20.5848 18.6953 20.2966 18.6953L17.1913 18.7156L17.085 16.9801C17.0598 16.6086 16.6046 16.4559 16.3669 16.7358L13.4032 20.2271C13.2616 20.3951 13.2768 20.6496 13.4386 20.7972L16.812 23.8915C17.0851 24.1409 17.52 23.9322 17.4947 23.5607L17.3885 21.8659L20.3168 21.8456C21.6773 21.8354 22.8809 21.1178 23.5333 19.9167C24.1908 18.7156 24.1504 17.3109 23.4322 16.1506V16.1557Z" />
    </svg>
  ),
  dashboard: (props: ComponentProps<"svg">) => (
    <svg width="18" height="18" {...props} viewBox="0 0 18 18" xmlns="http://www.w3.org/2000/svg">
      <path d="M1 10H7C7.55 10 8 9.55 8 9V1C8 0.45 7.55 0 7 0H1C0.45 0 0 0.45 0 1V9C0 9.55 0.45 10 1 10ZM1 18H7C7.55 18 8 17.55 8 17V13C8 12.45 7.55 12 7 12H1C0.45 12 0 12.45 0 13V17C0 17.55 0.45 18 1 18ZM11 18H17C17.55 18 18 17.55 18 17V9C18 8.45 17.55 8 17 8H11C10.45 8 10 8.45 10 9V17C10 17.55 10.45 18 11 18ZM10 1V5C10 5.55 10.45 6 11 6H17C17.55 6 18 5.55 18 5V1C18 0.45 17.55 0 17 0H11C10.45 0 10 0.45 10 1Z" />
    </svg>
  ),
  "declare-volumes": (props: ComponentProps<"svg">) => (
    <svg width="16" height="20" {...props} viewBox="0 0 16 20" xmlns="http://www.w3.org/2000/svg">
      <path d="M15.41 5.41L10.58 0.58C10.21 0.21 9.7 0 9.17 0H2C0.9 0 0.0100002 0.9 0.0100002 2L0 18C0 19.1 0.89 20 1.99 20H14C15.1 20 16 19.1 16 18V6.83C16 6.3 15.79 5.79 15.41 5.41ZM6.23 15.29L4.11 13.17C3.72 12.78 3.72 12.15 4.11 11.76C4.5 11.37 5.13 11.37 5.52 11.76L6.93 13.17L10.47 9.63C10.86 9.24 11.49 9.24 11.88 9.63C12.27 10.02 12.27 10.65 11.88 11.04L7.64 15.28C7.26 15.68 6.62 15.68 6.23 15.29ZM10 7C9.45 7 9 6.55 9 6V1.5L14.5 7H10Z" />
    </svg>
  ),
};

interface SidebarIconProps extends ComponentProps<"svg"> {
  icon: keyof typeof SIDEBAR_ICONS;
}

export function SidebarIcon({ icon, ...props }: SidebarIconProps) {
  const IconElement = SIDEBAR_ICONS[icon] as React.ComponentType<ComponentProps<"svg">>;

  if (!IconElement) return <p className="text-primary">Not Found</p>;

  return <IconElement {...props} />;
}
