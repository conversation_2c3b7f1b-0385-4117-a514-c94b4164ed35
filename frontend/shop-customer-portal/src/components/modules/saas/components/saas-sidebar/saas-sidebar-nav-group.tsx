"use client";

import { ArrowDropDown } from "@arthursenno/lizenzero-ui-react/Icon";
import { DetailsHTMLAttributes, ReactNode } from "react";

interface SaasSidebarNavGroupProps extends DetailsHTMLAttributes<HTMLDetailsElement> {
  title: string;
  icon: (props: any) => JSX.Element;
  children: ReactNode;
  startOpen?: boolean;
}

export function SaasSidebarNavGroup({
  title,
  icon: Icon,
  children,
  startOpen = false,
  ...props
}: SaasSidebarNavGroupProps) {
  return (
    <details
      className="
        open:bg-white w-full
      [&>summary]:has-[a[data-active=true]]:border-support-blue [&>summary]:has-[a[data-active=true]]:text-support-blue
      [&>summary>svg]:has-[a[data-active=true]]:fill-support-blue
      [&>summary>p]:has-[a[data-active=true]]:text-support-blue [&>summary>p]:has-[a[data-active=true]]:hover:underline
      [&>summary>.sidebar-chevron]:open:rotate-180
    "
      open={startOpen}
      {...props}
    >
      <summary className="px-6 py-4 flex items-center gap-4 cursor-pointer hover:bg-[#FCFCFC] border-l-[3px] border-white">
        <Icon width={24} height={24} className="sidebar-icon fill-primary size-6" />
        <p className="text-primary text-md font-bold underline-offset-2">{title}</p>
        <ArrowDropDown width={32} className="sidebar-chevron ml-auto fill-primary transition-all duration-300" />
      </summary>
      <div className="animate-slideDownAndFade transition-all duration-300">{children}</div>
    </details>
  );
}
