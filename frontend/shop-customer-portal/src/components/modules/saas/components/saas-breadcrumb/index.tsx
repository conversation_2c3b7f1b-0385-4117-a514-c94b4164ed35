import { cn } from "@/lib/utils";

import { BreadcrumbItems } from "@/components/ui/breadcrumb/breadcrumb";

interface SaasBreadcrumbProps {
  paths: { href: string; label: string }[];
  className?: string;
}

export function SaasBreadcrumb({ paths, className }: SaasBreadcrumbProps) {
  return (
    <div className={cn("w-full max-lg:px-4 py-10 lg:pl-32 lg:pr-28", className)}>
      <div className="w-full lg:max-w-[912px] mx-auto relative">
        <BreadcrumbItems paths={paths} />
      </div>
    </div>
  );
}
