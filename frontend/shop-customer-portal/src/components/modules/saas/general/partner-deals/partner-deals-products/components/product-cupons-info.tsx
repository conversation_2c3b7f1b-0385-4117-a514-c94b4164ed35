import { TextCopy } from "@/components/ui/text-copy";
import { cn } from "@/lib/utils";
import { IProductCupon } from "./interfaces";

interface IProductCuponsInfo extends IProductCupon {
  className?: string;
  isRow?: boolean;
}

export function ProductCuponsInfo({ className, isRow, percentageOff, subText, cupon }: IProductCuponsInfo) {
  return (
    <div
      className={cn(
        "bg-[#EAF1FE] py-4 px-5 rounded-[20px] w-full h-min gap-4",
        isRow && "flex flex-row justify-between items-center flex-wrap",
        className
      )}
    >
      <div>
        <p className="text-xl font-bold text-grey-blue">{percentageOff}% off</p>
        <p className="text-[#425981] text-base">{subText}</p>
      </div>
      <div className={cn(!isRow && "mt-4")}>
        <TextCopy text={cupon} className="min-w-[75%]" />
      </div>
    </div>
  );
}
