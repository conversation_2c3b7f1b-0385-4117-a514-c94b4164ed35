import { Link } from "@/i18n/navigation";
import { intlDateFormatter } from "@/lib/utils";
import { clientAcquisition, materials, partnerContract } from "@/utils/consts/partner-hub";
import { DEFAULT_CLIENT_ACQUISITION } from "@/utils/partner-hub";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Download, East, Edit, File } from "@arthursenno/lizenzero-ui-react/Icon";
import { BiLinkExternal } from "react-icons/bi";
import { FaCircle } from "react-icons/fa";

import { ContractStatus } from "./contract-status";
import { DashboardYearSelector } from "./dashboard-year-selector";
import { ClientAcquisitionChart } from "./client-acquisition-chart";
import { getServerUser } from "@/utils/get-server-user";
import { SaasBreadcrumb } from "../../saas/components/saas-breadcrumb";

interface PartnerDashboardProps {
  locale: string;
  year?: string;
}

export function PartnerDashboard({ locale, year }: PartnerDashboardProps) {
  const user = getServerUser();

  const latestMaterials = materials.slice(-4);

  const matchingAcquisition = clientAcquisition.acquisitions.find(
    (a) => a.year === (year || String(new Date().getFullYear()))
  );

  return (
    <div data-dashboard={true} className="mx-auto pt-6 pb-4 px-4 max-w-[928px] min-h-screen">
      <SaasBreadcrumb paths={[{ label: "Hub", href: `/${locale}/partner-hub` }]} />

      <div className="flex flex-col gap-4 pt-7 text-grey-blue max-w-[611px]">
        <h1 className="font-h1 text-h1 font-bold">Partner Hub</h1>
        <h4 className="text-paragraph-regular">
          Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc vulputate libero et velit interdum, ac aliquet
          odio mattis.
        </h4>
      </div>

      <div id="partner-graphs" className="grid grid-cols-7 pb-12 gap-6 pt-16">
        <div className="bg-[#EBF4FF] p-10 rounded-[40px] col-span-7 2xl:col-span-4 flex-shrink-0">
          <h2 className="text-[28px] font-bold text-tonal-dark-blue-10 pb-6">Contract</h2>
          <ContractStatus status={partnerContract.status} />
          <h4 className="text-xl pt-6 pb-4 text-tonal-dark-cream-10 font-bold overflow-hidden whitespace-nowrap text-ellipsis">
            Changes agreed via e-mail
          </h4>
          {partnerContract.changes.length > 0 ? (
            <ol className="flex flex-col gap-6 items-start text-tonal-dark-cream-10">
              {partnerContract.changes.map((change, index) => (
                <li key={`contract-changes-${index}`} className="flex gap-1">
                  <p>{index + 1}.</p>
                  <div>
                    <p>{change.description}</p>
                    <p className="text-sm text-tonal-dark-cream-50 pt-2">
                      Update in {intlDateFormatter.format(change.timestamp)}
                    </p>
                  </div>
                </li>
              ))}
            </ol>
          ) : (
            <p className="text-sm text-tonal-dark-cream-50">
              No changes. To add a change contact the marketing manager by e-mail.
            </p>
          )}
          {partnerContract.status === "agreed" && (
            <Link href={""}>
              <Button
                className="mt-6"
                variant="text"
                color="light-blue"
                size="medium"
                leadingIcon={<BiLinkExternal size={"1.25rem"} />}
              >
                My account
              </Button>
            </Link>
          )}
        </div>

        <div className="bg-white p-8 rounded-[20px] col-span-7 2xl:col-span-3 flex-shrink-0">
          <div className="pb-7">
            <div className="flex gap-2 flex-wrap justify-between">
              <h4 className="text-2xl font-bold text-tonal-dark-blue-10">My global statistics</h4>
              <DashboardYearSelector years={clientAcquisition.acquisitions.map((a) => a.year)} selectedYear={year} />
            </div>
            <p className="text-[#808FA9] pt-2">Values in thousands of euros</p>
          </div>

          <ClientAcquisitionChart data={matchingAcquisition || DEFAULT_CLIENT_ACQUISITION} />

          <div className="flex gap-4 justify-between pb-6 pt-10">
            <div className="flex flex-col gap-1 text-primary">
              <h5 className="text-[32px] font-bold">000</h5>
              <div className="flex gap-1 items-center text-xs">
                <FaCircle fill="#FF9E14" width={12} height={12} /> Direct License
              </div>
            </div>
            <div className="flex flex-col gap-1 text-primary">
              <h5 className="text-[32px] font-bold">000</h5>
              <div className="flex gap-1 items-center text-xs">
                <FaCircle fill="#1B6C64" width={12} height={12} /> EU License
              </div>
            </div>
            <div className="flex flex-col gap-1 text-primary">
              <h5 className="text-[32px] font-bold">000</h5>
              <div className="flex gap-1 items-center text-xs">
                <FaCircle fill="#F6DA62" width={12} height={12} /> Action Guide
              </div>
            </div>
          </div>
          <div className="p-6 bg-[#F7F5F2] rounded-2xl text-primary">
            <h3 className="text-[32px] font-bold">€ 00.00</h3>
            <p className="text-sm">Total earnings</p>
          </div>
          <Link href={`/partner-hub/commissions`}>
            <Button className="w-full mt-4" variant="filled" color="dark-blue" size="small" trailingIcon={<East />}>
              My commissions
            </Button>
          </Link>
        </div>
      </div>

      <div className="p-8 rounded-[20px] bg-[#FCFCFC]">
        <div className="flex gap-2 items-center">
          <h4 className="text-2xl font-bold font-large-paragraph-bold text-tonal-dark-blue-10">
            Latest materials added
          </h4>
          <Edit className="fill-support-blue w-6 h-6" />
        </div>
        <p className="text-[#808FA9] pt-3 pb-7">From the marketing materials</p>
        <div className="flex flex-col gap-4">
          {latestMaterials.length > 0 ? (
            latestMaterials.map((m) => (
              <div
                key={`campaign-materials-${m.id}`}
                className="flex gap-2 items-center flex-grow border-b border-tonal-dark-cream-80 px-2 py-4"
              >
                <File className="lg:w-[60px] lg:h-[60px] w-10 h-10 fill-primary flex-shrink-0" />
                <div className="flex flex-col flex-grow overflow-hidden">
                  <div className="flex lg:justify-between lg:items-center gap-x-2 gap-y-1 flex-col lg:flex-row flex-grow">
                    <h5 className="text-base text-tonal-dark-cream-10 overflow-hidden whitespace-nowrap text-ellipsis">
                      {m.name}
                    </h5>
                    <a target="_blank" href={m.url} rel="noopener noreferrer">
                      <Button variant="text" color="light-blue" size="small" leadingIcon={<Download />}>
                        Download
                      </Button>
                    </a>
                  </div>
                  <p className="text-tonal-dark-cream-40 text-sm pt-1">
                    {intlDateFormatter.format(m.date).replaceAll("/", ".")}
                  </p>
                </div>
              </div>
            ))
          ) : (
            <div className="w-full text-center text-tonal-dark-cream-10 py-6">No materials found.</div>
          )}
          <Link href={`/partner-hub/marketing-materials`}>
            <Button className="mt-3" variant="filled" color="dark-blue" size="small" trailingIcon={<East />}>
              Marketing Materials
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
}
