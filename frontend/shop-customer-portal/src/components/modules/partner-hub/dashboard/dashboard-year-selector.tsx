"use client";
import { SelectDropdown } from "@/components/_common/select-dropdown";
import { useRouter } from "@/i18n/navigation";

export function DashboardYearSelector({ selectedYear, years }: { years: string[]; selectedYear?: string }) {
  const router = useRouter();

  return (
    <SelectDropdown
      options={years.map((year) => ({
        label: year,
        value: year,
        disabled: year === selectedYear,
      }))}
      value={selectedYear || years[0]}
      onChangeValue={(year) => router.push(`/partner-hub?year=${year}`, { scroll: false })}
    />
  );
}
