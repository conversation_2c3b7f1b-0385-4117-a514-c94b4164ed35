"use client";
import { ChartConfig, ChartContainer } from "@/components/ui/chart";
import { clientAcquisition } from "@/utils/consts/partner-hub";
import { useMemo } from "react";
import { Label, Pie, PieChart } from "recharts";

const config = {
  "direct-license": {
    color: "#ff9e14",
    label: "Direct License",
  },
  "eu-license": {
    color: "#1B6C64",
    label: "EU License",
  },
  "action-guide": {
    color: "#F6DA62",
    label: "Action Guide",
  },
} satisfies ChartConfig;

export function ClientAcquisitionChart({ data }: { data: (typeof clientAcquisition)["acquisitions"][number] }) {
  const totalClients = useMemo(() => {
    return data.clients.reduce((acc, curr) => acc + curr.amount, 0);
  }, []);

  const chartData = data.clients.map((client) => ({
    ...client,
    amount: totalClients > 0 ? client.amount : 1 / 3,
    fill: config[client.method].color,
  }));

  return (
    <ChartContainer config={config} className="mx-auto aspect-square max-h-[265px]">
      <PieChart>
        <Pie
          data={chartData}
          dataKey="amount"
          nameKey="method"
          innerRadius={85}
          strokeWidth={0}
          // startAngle={0}
          // endAngle={360}
        >
          <Label
            content={({ viewBox }) => {
              if (viewBox && "cx" in viewBox && "cy" in viewBox) {
                return (
                  <text x={viewBox.cx} y={viewBox.cy} textAnchor="middle" dominantBaseline="middle">
                    <tspan x={viewBox.cx} y={(viewBox.cy || 0) - 12} className="fill-primary text-3xl font-bold">
                      {totalClients.toLocaleString()}
                    </tspan>
                    <tspan x={viewBox.cx} y={(viewBox.cy || 0) + 12} className="fill-primary text-sm">
                      Total
                    </tspan>
                    <tspan x={viewBox.cx} y={(viewBox.cy || 0) + 28} className="fill-primary text-sm">
                      acquired clients
                    </tspan>
                  </text>
                );
              }
            }}
          />
        </Pie>
      </PieChart>
    </ChartContainer>
  );
}
