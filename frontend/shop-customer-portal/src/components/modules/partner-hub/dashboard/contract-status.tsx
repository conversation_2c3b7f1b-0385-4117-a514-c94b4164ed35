import { PartnerContract } from "@/types/partner-hub/partner-contract";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Download, File } from "@arthursenno/lizenzero-ui-react/Icon";

export function ContractStatus({ status }: { status: PartnerContract["status"] }) {
  if (status === "in-preparation") {
    return (
      <div className="flex gap-2 items-center flex-grow border-b border-tonal-dark-cream-80 px-2 pt-4 pb-6">
        <File className="lg:w-[60px] lg:h-[60px] w-10 h-10 fill-on-surface-04 flex-shrink-0" />
        <div className="flex flex-col flex-grow overflow-hidden">
          <div className="flex items-start lg:justify-between lg:items-center gap-1 flex-col lg:flex-row flex-grow">
            <h4 className="text-xl text-primary font-bold overflow-hidden whitespace-nowrap text-ellipsis">
              Partnership Contract
            </h4>
            <div className="rounded-lg px-4 py-2 bg-surface-04 text-on-surface-04 font-bold flex flex-shrink-0">
              Contract in preparation
            </div>
          </div>
          <p className="text-tonal-dark-cream-40 text-sm pt-1">The marketing team is creating your contract.</p>
        </div>
      </div>
    );
  }

  if (status === "pending") {
    return (
      <div className="flex gap-2 items-center flex-grow border-b border-tonal-dark-cream-80 px-2 pt-4 pb-6">
        <File className="lg:w-[60px] lg:h-[60px] w-10 h-10 fill-tonal-dark-cream-50 flex-shrink-0" />
        <div className="flex flex-col flex-grow overflow-hidden">
          <div className="flex lg:justify-between lg:items-center gap-1 flex-col lg:flex-row flex-grow">
            <h4 className="text-xl text-tonal-dark-cream-20 font-bold overflow-hidden whitespace-nowrap text-ellipsis">
              Partnership Contract
            </h4>
            <a target="_blank" href={""} rel="noopener noreferrer">
              <Button variant="text" color="light-blue" size="medium" leadingIcon={<Download />}>
                Download
              </Button>
            </a>
          </div>
          <p className="text-tonal-dark-cream-40 text-sm pt-1">
            <Button variant="filled" color="yellow" size="small" leadingIcon={<></>} trailingIcon={<></>}>
              I agree with the contract
            </Button>
          </p>
        </div>
      </div>
    );
  }

  if (status === "agreed") {
    return (
      <div className="flex gap-2 items-center flex-grow border-b border-tonal-dark-cream-80 px-2 pt-4 pb-6">
        <File className="lg:w-[60px] lg:h-[60px] w-10 h-10 fill-primary flex-shrink-0" />
        <div className="flex flex-col flex-grow overflow-hidden">
          <div className="flex lg:justify-between lg:items-center gap-1 flex-col lg:flex-row flex-grow">
            <h4 className="text-xl text-primary font-bold overflow-hidden whitespace-nowrap text-ellipsis">
              Partnership Contract
            </h4>
            <a target="_blank" href={""} rel="noopener noreferrer">
              <Button variant="text" color="light-blue" size="medium" leadingIcon={<Download />}>
                Download
              </Button>
            </a>
          </div>
          <p className="text-tonal-dark-cream-40 text-sm pt-1">
            {(() => {
              const date = new Date();
              const formattedDateTime = date.toLocaleString("en-GB", {
                timeZone: "UTC",
              });
              const offset = -1 * (date.getTimezoneOffset() / 60);
              return `Agreed on: ${formattedDateTime} GMT${offset >= 0 ? `+${offset}` : offset}`;
            })()}
          </p>
        </div>
      </div>
    );
  }

  return null;
}
