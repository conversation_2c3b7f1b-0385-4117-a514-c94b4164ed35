"use client";

import { Check } from "@arthursenno/lizenzero-ui-react/Icon";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { useFormContext, useWatch } from "react-hook-form";
import { PartnerInformationFormData } from "./partner-information-provider";

export function PartnerInformationBankingForm() {
  const form = useFormContext<PartnerInformationFormData>();

  const errors = form.formState.errors;

  const internationalAccountNumber = useWatch({ control: form.control, name: "internationalAccountNumber" });
  const businessIdentifierCode = useWatch({ control: form.control, name: "businessIdentifierCode" });

  return (
    <div className="w-full md:w-3/5 rounded-[32px] items-start bg-surface-02 flex flex-col px-4 py-6 md:py-7 md:px-8">
      <div className="flex flex-col gap-4 mb-8">
        <p className="text-primary font-medium text-xl">Banking</p>
        <p className="text-primary">This information will be used to transfer the commissions to your bank account.</p>
      </div>

      <div className="space-y-6 w-full">
        <Input
          {...form.register("internationalAccountNumber")}
          label="International Bank Account Number"
          rightIcon={
            !errors.internationalAccountNumber &&
            internationalAccountNumber && <Check width={20} height={20} className="fill-tonal-green-40" />
          }
          variant={errors.internationalAccountNumber && "error"}
          errorMessage={errors.internationalAccountNumber?.message}
          placeholder="International Bank Account Number"
        />

        <Input
          {...form.register("businessIdentifierCode")}
          label="Business Identifier Code"
          rightIcon={
            !errors.businessIdentifierCode &&
            businessIdentifierCode && <Check width={20} height={20} className="fill-tonal-green-40" />
          }
          variant={errors.businessIdentifierCode && "error"}
          errorMessage={errors.businessIdentifierCode?.message}
          placeholder="Business Identifier Code"
        />
      </div>
    </div>
  );
}
