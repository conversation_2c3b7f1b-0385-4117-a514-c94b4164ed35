"use client";

import { Divider } from "@/components/_common/divider";
import { PhoneInput } from "@/components/_common/forms/phone-input";
import { FormInputIcon } from "@/components/_common/input-status-icon";
import { Combobox } from "@/components/ui/combobox";
import { COUNTRIES } from "@/utils/consts/countries";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import Image from "next/image";
import { useFormContext } from "react-hook-form";
import { PartnerInformationFormData } from "./partner-information-provider";

const today = new Date().toISOString().split("T")[0];

export function PartnerInformationCompanyForm() {
  const form = useFormContext<PartnerInformationFormData>();

  const addressLine = form.watch("addressLine");
  const countryCode = form.watch("countryCode");
  const contactPhone = form.watch("contactPhone");

  function handleSelectCountry(country: { value: string; label: string } | null) {
    form.setValue("city", "");
    form.setValue("zipCode", "");
    form.setValue("streetAndNumber", "");
    form.setValue("addressLine", "");
    form.setValue("additionalAddressLine", "");

    if (!country) return;

    const countryName = country.value;

    const foundCountry = COUNTRIES.find((c) => c.name === countryName);

    if (!foundCountry) return;

    form.setValue("countryCode", foundCountry.code);
    form.clearErrors(`countryCode`);
  }

  const isSubmitted = form.formState.isSubmitted;
  const errors = form.formState.errors;

  return (
    <div className="w-full md:w-3/5 rounded-[32px] items-start bg-surface-02 flex flex-col px-4 py-6 md:py-7 md:px-8">
      <div className="flex w-full flex-row justify-between align-middle mb-8">
        <p className="text-primary font-medium text-xl">Company Information</p>
      </div>

      <div className="space-y-6 w-full">
        <Input
          {...form.register("companyName")}
          label="Company Name"
          placeholder="Company Name"
          rightIcon={<FormInputIcon control={form.control} name="companyName" />}
          variant={errors.companyName && "error"}
          errorMessage={errors.companyName?.message}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Input
            {...form.register("startingDateOfCooperation")}
            label="Starting date of the cooperation"
            type="date"
            variant={errors.startingDateOfCooperation && "error"}
            errorMessage={errors.startingDateOfCooperation?.message}
            rightIcon={<FormInputIcon control={form.control} name="startingDateOfCooperation" />}
            max={today}
          />
          <div />
        </div>

        <Input
          {...form.register("partnerOfferWebsite")}
          label="Partner's offer website"
          placeholder="www.link.com"
          rightIcon={<FormInputIcon control={form.control} name="partnerOfferWebsite" />}
          variant={errors.partnerOfferWebsite && "error"}
          errorMessage={errors.partnerOfferWebsite?.message}
        />

        <Input
          {...form.register("descriptionOfBusiness")}
          label="Description of business"
          placeholder="Description of business"
          rightIcon={<FormInputIcon control={form.control} name="descriptionOfBusiness" />}
          variant={errors.descriptionOfBusiness && "error"}
          errorMessage={errors.descriptionOfBusiness?.message}
        />

        <Input
          {...form.register("managingDirector")}
          label="Managing Director/CEO's Name"
          placeholder="Managing Director/CEO's Name"
          rightIcon={<FormInputIcon control={form.control} name="managingDirector" />}
          variant={errors.managingDirector && "error"}
          errorMessage={errors.managingDirector?.message}
        />

        <div className="grid md:grid-cols-2 w-full gap-8">
          <div className="space-y-2">
            <p className="text-primary">Country *</p>
            <Combobox
              key={"partner"}
              items={COUNTRIES.map((c) => ({ label: c.name, value: c.name, flag_url: c.flag_url }))}
              placeholder="Select a country"
              emptyText="No country found"
              searchText="Search country..."
              value={COUNTRIES.find((c) => c.code === countryCode)?.name || ""}
              onSelect={handleSelectCountry}
              invalid={!!errors.countryCode}
              renderItem={(item) => (
                <div className="flex items-center gap-3">
                  <div className="overflow-hidden h-6 w-6 rounded-full flex-none">
                    <Image
                      className="h-full w-full object-cover"
                      width={20}
                      height={20}
                      src={item.flag_url}
                      alt={`${item.label} flag`}
                    />
                  </div>
                  {item.label}
                </div>
              )}
            />
            {!!errors.countryCode && (
              <div className="flex justify-start items-center mt-2.5 space-x-2 ">
                <span slot="errorMessage" className="font-centra text-sm  text-tonal-red-40">
                  {errors.countryCode.message}
                </span>
              </div>
            )}
          </div>
          <Input
            label="Address *"
            placeholder="Your address"
            rightIcon={<FormInputIcon control={form.control} name="addressLine" />}
            errorMessage={errors.addressLine?.message}
            {...form.register("addressLine")}
            variant={errors.addressLine && "error"}
            enabled={!!countryCode}
          />
        </div>
        <div className="grid md:grid-cols-2 w-full gap-8">
          <Input
            {...form.register("city")}
            label="City *"
            placeholder="City"
            rightIcon={<FormInputIcon control={form.control} name="city" />}
            errorMessage={errors.city?.message}
            variant={errors.city && "error"}
            enabled={!!countryCode && !!addressLine}
          />
          <Input
            {...form.register("zipCode")}
            label="ZIP Code *"
            placeholder="ZIP Code"
            errorMessage={errors.zipCode?.message}
            rightIcon={<FormInputIcon control={form.control} name="zipCode" />}
            variant={errors.zipCode && "error"}
            enabled={!!countryCode && !!addressLine}
          />
        </div>
        <div className="grid md:grid-cols-2 w-full gap-8">
          <Input
            {...form.register("streetAndNumber")}
            label="Street and number *"
            placeholder="Street and number"
            rightIcon={<FormInputIcon control={form.control} name="streetAndNumber" />}
            errorMessage={errors.streetAndNumber?.message}
            variant={errors.streetAndNumber && "error"}
            enabled={!!countryCode && !!addressLine}
          />
          <Input
            {...form.register("additionalAddressLine")}
            label="Additional address line"
            placeholder="Additional address line"
            errorMessage={errors.additionalAddressLine?.message}
            rightIcon={<FormInputIcon control={form.control} name="additionalAddressLine" />}
            variant={errors.additionalAddressLine && "error"}
          />
        </div>
      </div>

      <Divider />

      <p className="text-primary font-medium text-xl mb-8">Contact</p>

      <div className="space-y-6 w-full">
        <Input
          {...form.register("contactName")}
          label="Contact Name"
          placeholder="Contact Name"
          rightIcon={<FormInputIcon control={form.control} name="contactName" />}
          variant={errors.contactName && "error"}
          errorMessage={errors.contactName?.message}
        />

        <div className="grid grid-cols-2 gap-8">
          <Input
            {...form.register("contactEmail")}
            label="E-mail"
            placeholder="<EMAIL>"
            rightIcon={<FormInputIcon control={form.control} name="contactEmail" />}
            variant={errors.contactEmail && "error"}
            errorMessage={errors.contactEmail?.message}
          />

          <div className="space-y-2">
            <p className="text-primary">Phone / Mobile</p>
            <PhoneInput
              defaultValue={contactPhone}
              name="contactPhone"
              valueSetter={(value) => form.setValue("contactPhone", value)}
              errorSetter={(valid) =>
                valid
                  ? form.clearErrors("contactPhone")
                  : form.setError("contactPhone", { message: "Invalid phone number." })
              }
              isError={!!errors.contactPhone}
              required={false}
            />
            {!!errors.contactPhone && (
              <div className="flex justify-start items-center mt-2.5 space-x-2 ">
                <span slot="errorMessage" className="font-centra text-sm  text-tonal-red-40">
                  {errors.contactPhone.message}
                </span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
