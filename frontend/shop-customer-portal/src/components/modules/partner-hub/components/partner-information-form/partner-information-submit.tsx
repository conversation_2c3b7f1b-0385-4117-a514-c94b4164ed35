"use client";

import { usePart<PERSON> } from "@/hooks/use-partner";
import { useRouter } from "@/i18n/navigation";
import { createPartner } from "@/lib/api/partner";
import { queryClient } from "@/lib/react-query";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { East } from "@arthursenno/lizenzero-ui-react/Icon";
import { useSession } from "next-auth/react";
import { enqueueSnackbar } from "notistack";
import { useFormContext } from "react-hook-form";

export function PartnerInformationSubmit() {
  const session = useSession();

  const { partner } = usePartner();
  const router = useRouter();

  const form = useFormContext();

  const hasErrors = !!Object.keys(form.formState.errors).length;

  const buttonColor = hasErrors ? "red" : "yellow";
  const disabled = form.formState.isSubmitting || hasErrors;
  const label = form.formState.isSubmitting ? "Loading..." : "Submit data";

  async function handleSkip() {
    if (!session.data?.user) return router.push("/partner-hub/");

    const user = session.data.user;

    if (partner) router.push("/partner-hub/");

    const createPartnerResponse = await createPartner({
      partner_firstname: user.first_name,
      partner_lastname: user.last_name,
      partner_email: user.email,
      user_id: Number(user.id),
    });

    if (!createPartnerResponse) {
      enqueueSnackbar("Error when saving data", { variant: "error" });
      return;
    }

    router.push("/partner-hub/");
    queryClient.invalidateQueries({ queryKey: ["partner"] });
  }

  return (
    <div className="w-full md:w-3/5 flex flex-col sm:flex-row gap-6 justify-end">
      <Button
        onClick={handleSkip}
        type="button"
        color="dark-blue"
        variant="outlined"
        size="medium"
        form="ph-review-info-form"
        className="w-full md:w-60"
      >
        Skip
      </Button>

      <Button
        type="submit"
        color={buttonColor}
        disabled={disabled}
        variant="filled"
        size="medium"
        form="ph-review-info-form"
        trailingIcon={!form.formState.isSubmitting && <East />}
        className="w-full md:w-60"
      >
        {label}
      </Button>
    </div>
  );
}
