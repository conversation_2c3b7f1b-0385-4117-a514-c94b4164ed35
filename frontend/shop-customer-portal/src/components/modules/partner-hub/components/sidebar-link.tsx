"use client";

import { Link } from "@/i18n/navigation";
import { usePathname } from "@/i18n/navigation";
import { ReactNode } from "react";

export function SidebarLink({ children, href }: { children: ReactNode; href: string }) {
  const pathname = usePathname();

  return (
    <Link
      data-active={href === pathname}
      href={href}
      className="block px-6 py-4 font-bold border-l-2 border-[transparent] text-tonal-dark-blue-10 hover:underline data-[active=true]:border-support-blue data-[active=true]:text-support-blue"
    >
      {children}
    </Link>
  );
}
