"use client";

import { Logout } from "@arthursenno/lizenzero-ui-react/Icon";
import { signOut } from "next-auth/react";

export function LogoutMenuItem() {
  return (
    <button
      type="button"
      onClick={() => signOut({ redirect: true })}
      className="cursor-pointer w-full block lg:hidden px-6 py-4 font-bold border-l-2 border-[transparent] text-tonal-dark-blue-10 hover:underline"
    >
      <div className="flex gap-4 items-center">
        <Logout width={24} className="fill-primary" />
        Logout
      </div>
    </button>
  );
}
