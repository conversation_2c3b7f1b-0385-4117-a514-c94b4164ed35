"use client";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { Icons } from "@/components/ui/icons";
import { PasswordInput } from "@/components/ui/password-input";
import { use<PERSON><PERSON><PERSON> } from "@/hooks/use-partner";
import { <PERSON> } from "@/i18n/navigation";
import { patchPassword } from "@/lib/api/auth";
import { CONTAINS_LETTER_REGEX, CONTAINS_NON_ALPHANUMERIC_REGEX, CONTAINS_NUMBER_REGEX } from "@/utils/regex";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { East } from "@arthursenno/lizenzero-ui-react/Icon";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { zodResolver } from "@hookform/resolvers/zod";
import { enqueueSnackbar } from "notistack";
import { PasswordStrengthBar } from "@/components/_common/password-strength-bar";

const passwordSchema = z.object({
  newPassword: z
    .string()
    .min(6, "New password must have at least 6 characters")
    .regex(CONTAINS_LETTER_REGEX, "Enter a letter")
    .regex(CONTAINS_NUMBER_REGEX, "Enter a number")
    .regex(CONTAINS_NON_ALPHANUMERIC_REGEX, "Enter a special character"),
  newPasswordConfirmation: z.string().min(6, { message: "Confirm Password is required" }),
  password: z.string().min(6, { message: "Current Password is required" }),
});

type PasswordFormValues = z.infer<typeof passwordSchema>;

export function PartnerPasswordForm() {
  const { partner } = usePartner();

  const [editingLock, setEditingLock] = useState(true);
  const [pass, setPass] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const {
    handleSubmit,
    register,
    getValues,
    setError,
    clearErrors,
    reset,
    watch,
    formState: { errors },
  } = useForm<PasswordFormValues>({
    resolver: zodResolver(passwordSchema),
  });

  const hasErrors = Object.keys(errors).length > 0;

  const newPassword = watch("newPassword");
  const newPasswordConfirmation = watch("newPasswordConfirmation");

  const isNotValidConfirmPassword = newPassword && newPasswordConfirmation && newPassword !== newPasswordConfirmation;

  async function cancelEditing() {
    reset();
    setEditingLock(true);
  }

  async function submit(data: PasswordFormValues) {
    if (!partner) return;

    if (isNotValidConfirmPassword) return;

    setIsLoading(true);

    try {
      const res: any = await patchPassword(partner.user_id, {
        newPassword: data.newPassword,
        oldPassword: data.password,
      });

      if (!res?.data) {
        const status = res?.response?.status;

        if (status === 401) {
          setError("password", { message: "Wrong password" });
        }
        return;
      }

      setEditingLock(true);
      reset();
      enqueueSnackbar("Password edited successfully", { variant: "success" });
    } catch (error) {
      enqueueSnackbar("An error occurred while updating the user data", { variant: "error" });
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div>
      <div className="w-full rounded-[32px] items-start bg-surface-02 flex flex-col px-4 py-6 md:py-7 md:px-8">
        <p className="text-primary font-medium text-xl mb-8">Change Password</p>
        {editingLock ? (
          <div className="grid grid-cols-2 gap-6">
            <Input label="Password" placeholder="*******" enabled={false} variant={"disabled"} type="password" />

            <div
              onClick={() => setEditingLock(false)}
              className="flex items-center gap-2 cursor-pointer h-fit w-fit self-center"
            >
              <span className="text-support-blue font-bold">Change password</span>
              <Icons.pencil className="fill-support-blue" />
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-2 gap-6 w-full">
            <div className="flex flex-col gap-3">
              <PasswordInput
                label="New password"
                {...register("newPassword", {
                  onChange: (e) => {
                    setPass(e.target.value);
                  },
                })}
                placeholder="New password"
                errorMessage={errors.newPassword && errors.newPassword.message}
                variant={errors.newPassword && "error"}
              />

              <PasswordStrengthBar />
            </div>

            <PasswordInput
              label="Confirm password"
              {...register("newPasswordConfirmation")}
              placeholder="New password"
              variant={(errors.newPasswordConfirmation || isNotValidConfirmPassword) && "error"}
              errorMessage={
                (errors.newPasswordConfirmation && errors.newPasswordConfirmation.message) ||
                (isNotValidConfirmPassword && "The password doesnt match")
              }
            />

            <div className="space-y-2">
              <PasswordInput
                label="Current password"
                {...register("password")}
                placeholder="Current password"
                variant={errors.password && "error"}
              />
              {!!errors.password && (
                <div className="flex justify-start items-center mt-2.5 space-x-2 ">
                  <span slot="errorMessage" className="font-centra text-sm  text-tonal-red-40">
                    {errors.password.message}
                  </span>
                  <Link href={"/en/partner-hub/auth/forgot-password"} className="text-support-blue text-sm font-bold">
                    Forgot password?
                  </Link>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {!editingLock && (
        <div className="flex flex-row gap-6 justify-end w-full mt-8">
          <Button onClick={cancelEditing} color="dark-blue" size="medium" variant="outlined" type="button">
            Cancel
          </Button>
          <Button
            color="yellow"
            trailingIcon={<East />}
            size="medium"
            variant="filled"
            type="button"
            disabled={hasErrors || isNotValidConfirmPassword || isLoading}
            onClick={handleSubmit(submit)}
          >
            {isLoading ? "Loading..." : "Save"}
          </Button>
        </div>
      )}
    </div>
  );
}
