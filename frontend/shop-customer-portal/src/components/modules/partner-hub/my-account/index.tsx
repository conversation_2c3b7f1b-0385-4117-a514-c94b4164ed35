"use client";

import { useFormContext } from "react-hook-form";
import { PhContractForm } from "../components/forms/ph-contract";
import { PartnerInformationBankingForm } from "../components/partner-information-form/partner-information-banking";
import { PartnerInformationCompanyForm } from "../components/partner-information-form/partner-information-company";
import { PartnerInformationFormData } from "../components/partner-information-form/partner-information-provider";
import { PartnerInformationSubmit } from "../components/partner-information-form/partner-information-submit";
import { PartnerEmailForm } from "./partner-email-form";
import { PartnerPasswordForm } from "./partner-password-form";

export function PartnerAccount() {
  const form = useFormContext<PartnerInformationFormData>();

  async function handleFormSubmit(data: PartnerInformationFormData) {
    console.log(data);
  }

  return (
    <>
      <PhContractForm />
      <form id="partner-review-information-form" onSubmit={form.handleSubmit(handleFormSubmit)}>
        <div className="space-y-12">
          <PartnerInformationCompanyForm />
          <PartnerInformationBankingForm />
          <PartnerEmailForm />
          <PartnerPasswordForm />
          <PartnerInformationSubmit />
        </div>
      </form>
    </>
  );
}
