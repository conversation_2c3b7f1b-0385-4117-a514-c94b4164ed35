"use client";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { Icons } from "@/components/ui/icons";
import { PasswordInput } from "@/components/ui/password-input";
import { usePartner } from "@/hooks/use-partner";
import { Link } from "@/i18n/navigation";
import { TypeResendToken } from "@/lib/api/account/types";
import { patchEmail, patchVerifyEmail } from "@/lib/api/auth";
import { updatePartner } from "@/lib/api/partner";
import { EMAIL_REGEX } from "@/utils/regex";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Check, East } from "@arthursenno/lizenzero-ui-react/Icon";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { zodResolver } from "@hookform/resolvers/zod";
import { enqueueSnackbar } from "notistack";
import { VerifyAccountModal } from "@/components/_common/modals/verify-account-modal";

const emailSchema = z.object({
  newEmail: z.string().regex(EMAIL_REGEX, { message: "Invalid email" }),
  newEmailConfirmation: z.string().regex(EMAIL_REGEX, { message: "Invalid email" }),
  password: z.string(),
});

export type EmailFormValues = z.infer<typeof emailSchema>;

const initialValues = {
  newEmail: "",
  newEmailConfirmation: "",
  password: "",
};

export function PartnerEmailForm() {
  const [editingLock, setEditingLock] = useState(true);
  const [open, setOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const { partner } = usePartner();

  const {
    handleSubmit,
    register,
    getValues,
    setError,
    clearErrors,
    reset,
    watch,
    formState: { errors },
  } = useForm<EmailFormValues>({
    resolver: zodResolver(emailSchema),
    defaultValues: initialValues,
  });

  const hasErrors = Object.keys(errors).length > 0;

  const newEmail = watch("newEmail");
  const newEmailConfirmation = watch("newEmailConfirmation");
  const password = watch("password");

  const isNotValidConfirmEmail = newEmail && newEmailConfirmation && newEmail !== newEmailConfirmation;

  const cancelEditing = () => {
    clearErrors();
    reset();
    setEditingLock(true);
  };

  async function submit(data: EmailFormValues) {
    if (!partner) return;
    setIsLoading(true);

    const res: any = await patchEmail(partner?.user_id, {
      newEmail: data.newEmail,
      oldEmail: partner.email || "",
      password: data.password,
    });

    if (res?.data) {
      setEditingLock(true);
      setOpen(true);
    }
    const status = res?.response?.status;

    if (status === 401) {
      setError("password", { message: "Wrong password" });
    }

    if (status === 409) {
      setError("newEmail", { message: "Email already in use" });
    }

    await updatePartner(partner.id, { partner_email: data.newEmail });

    setIsLoading(false);
  }

  async function handleConfirmToken(token: string) {
    if (!partner) return { error: true };

    const res: any = await patchVerifyEmail(partner.user_id, {
      token,
    });

    if (res.data) {
      setEditingLock(true);
      setOpen(false);
      reset();

      enqueueSnackbar("Email edited successfully", { variant: "success" });
    }

    return {
      error: !res.data,
    };
  }

  const handleResendToken = async () => {
    if (!partner) return { error: true };

    const res: any = await patchEmail(partner.user_id, {
      newEmail: newEmail,
      oldEmail: partner.email || "",
      password,
    });

    return {
      error: !res.data,
    };
  };

  return (
    <div>
      <div className="w-full rounded-[32px] items-start bg-surface-02 flex flex-col px-4 py-6 md:py-7 md:px-8">
        <p className="text-primary font-medium text-xl mb-8">Change Email</p>
        {editingLock ? (
          <div className="grid grid-cols-2 gap-6">
            <Input
              label="E-mail"
              value={partner?.email}
              placeholder="your email"
              enabled={false}
              variant={"disabled"}
            />

            <div
              onClick={() => setEditingLock(false)}
              className="flex items-center gap-2 cursor-pointer h-fit w-fit self-center"
            >
              <span className="text-support-blue font-bold">Change Email</span>
              <Icons.pencil className="fill-support-blue" />
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-2 gap-6 w-full">
            <Input
              label="New E-mail"
              {...register("newEmail")}
              placeholder="New email"
              variant={errors.newEmail && "error"}
              rightIcon={
                !errors.newEmail &&
                getValues("newEmail") && <Check width={20} height={20} className="fill-tonal-green-40" />
              }
              errorMessage={errors.newEmail?.message}
            />

            <Input
              label="Confirm E-mail"
              {...register("newEmailConfirmation")}
              placeholder="New email"
              rightIcon={
                !errors.newEmailConfirmation &&
                getValues("newEmailConfirmation") && <Check width={20} height={20} className="fill-tonal-green-40" />
              }
              variant={(errors.newEmailConfirmation || isNotValidConfirmEmail) && "error"}
              errorMessage={
                (errors.newEmailConfirmation && errors.newEmailConfirmation.message) ||
                (isNotValidConfirmEmail && "The email doesnt match")
              }
            />

            <div className="space-y-2">
              <PasswordInput
                label="Password"
                {...register("password")}
                placeholder="Your password"
                variant={errors.password && "error"}
              />

              {!!errors.password && (
                <div className="flex justify-start items-center mt-2.5 space-x-2 ">
                  <span slot="errorMessage" className="font-centra text-sm  text-tonal-red-40">
                    {errors.password.message}
                  </span>
                  <Link href={"/en/partner-hub/auth/forgot-password"} className="text-support-blue text-sm font-bold">
                    Forgot password?
                  </Link>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {!editingLock && (
        <div className="flex flex-row gap-6 justify-end w-full mt-8">
          <p className="text-error">{errors.root?.message}</p>
          <Button onClick={cancelEditing} color="dark-blue" size="medium" variant="outlined" type="button">
            Cancel
          </Button>

          <Button
            color="yellow"
            trailingIcon={<East />}
            size="medium"
            variant="filled"
            type="button"
            disabled={hasErrors || isNotValidConfirmEmail || isLoading}
            onClick={handleSubmit(submit)}
          >
            {isLoading ? "Loading..." : "Save"}
          </Button>
        </div>
      )}
      <VerifyAccountModal
        open={open}
        onOpenChange={setOpen}
        email={newEmail}
        typeResendToken={TypeResendToken.LOGIN}
        onConfirmToken={handleConfirmToken}
        onResendToken={handleResendToken}
      />
    </div>
  );
}
