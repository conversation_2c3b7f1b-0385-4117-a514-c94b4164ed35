import { <PERSON><PERSON><PERSON>r, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { InviteResult } from "@/types/partner-hub/invite";
import { useMemo } from "react";
import { Area, AreaChart, CartesianGrid, XAxis, YAxis } from "recharts";

const config = {
  code: {
    color: "#FFCE00",
  },
  link: {
    color: "#009DD3",
  },
};

type ChartInviteEntry = {
  invite1Value?: number;
  invite2Value?: number;
  period: string;
};

interface MyCommissionsInviteComparisonChartProps {
  data: [InviteResult | undefined, InviteResult | undefined];
}

export function MyCommissionsInviteComparisonChart({ data }: MyCommissionsInviteComparisonChartProps) {
  const xAxisMeasure = data[0]?.period === "m" ? "month" : "year";

  const chartData = useMemo(() => {
    const invitesData = [data[0], data[1]].reduce((acc, curr) => {
      if (!curr) return acc;

      const isInvite1 = curr.invite.id === data[0]?.invite?.id;

      const inviteEntries = curr.results.map((r) => ({
        value: r.value,
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        period: r.year || r.month,
        invite: curr.invite,
      }));

      inviteEntries.forEach((e) => {
        const existingEntryIndex = acc.findIndex((entry) => entry.period === e.period);

        if (existingEntryIndex !== -1) {
          acc[existingEntryIndex] = {
            ...acc[existingEntryIndex],
            [isInvite1 ? "invite1Value" : "invite2Value"]: e.value,
          };
        } else {
          if (isInvite1) {
            acc.push({
              period: e.period,
              invite1Value: e.value,
            });
          } else {
            acc.push({
              period: e.period,
              invite2Value: e.value,
            });
          }
        }
      });

      return acc;
    }, [] as ChartInviteEntry[]);

    return invitesData;
  }, [data]);

  return (
    <div className="flex items-center w-full pt-6">
      <p className="[text-orientation:sideways-right] [writing-mode:tb] rotate-180 text-tonal-dark-cream-10">
        Net turnover
      </p>
      <ChartContainer
        config={{
          invite1Value: {
            label: data[0]?.invite.value,
            color: config[data[0]?.invite.type || "code"].color,
          },
          invite2Value: {
            label: data[1]?.invite.value,
            color: config[data[1]?.invite.type || "code"].color,
          },
        }}
        className="mx-auto aspect-square max-h-[265px] w-full"
      >
        <AreaChart accessibilityLayer data={chartData}>
          <CartesianGrid vertical={false} />
          <YAxis tickLine={false} axisLine={false} tickMargin={4} />
          <XAxis
            dataKey="period"
            tickLine={false}
            tickMargin={8}
            axisLine={false}
            tickFormatter={(value) => (xAxisMeasure === "month" ? value.slice(0, 3) : value)}
          />
          <ChartTooltip cursor={false} content={<ChartTooltipContent indicator="dot" />} />
          <Area
            dataKey="invite1Value"
            stroke={config[data[0]?.invite.type || "code"].color}
            fill={config[data[0]?.invite.type || "code"].color}
            type="bump"
            fillOpacity={0.4}
            stackId="invite1"
          />
          <Area
            dataKey="invite2Value"
            stroke={config[data[1]?.invite.type || "code"].color}
            fill={config[data[1]?.invite.type || "code"].color}
            type="bump"
            fillOpacity={0.4}
            stackId="invite2"
          />
        </AreaChart>
      </ChartContainer>
    </div>
  );
}
