"use client";

import { CommissionsSearchParams } from "@/app/[locale]/partner-hub/(dashboard)/commissions/page";
import { SelectDropdown } from "@/components/_common/select-dropdown";
import { useRouter } from "@/i18n/navigation";
import { intlDateFormatter } from "@/lib/utils";
import { Invite } from "@/types/partner-hub/invite";
import { commissionProducts } from "@/utils/partner-hub";
import { joinSearchParams } from "@/utils/path";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { FileCopy } from "@arthursenno/lizenzero-ui-react/Icon";

interface MyCommissionsInviteShareProps {
  invites: Invite[];
  searchParams: CommissionsSearchParams;
}

export function MyCommissionsInviteShare({ invites, searchParams }: MyCommissionsInviteShareProps) {
  const router = useRouter();

  const selectedProduct =
    commissionProducts.find((p) => p.value === searchParams["inviteShareProduct"]) ||
    commissionProducts[commissionProducts.length - 1];

  const codeInvites = invites.filter((i) => i.type === "code");

  const displayedInvites =
    selectedProduct.value === "all" ? codeInvites : codeInvites.filter((i) => i.product === selectedProduct.value);

  return (
    <div className="flex flex-col gap-6">
      <SelectDropdown
        options={commissionProducts.map((product) => ({
          label: product.label,
          value: product.value,
          disabled: product.value === selectedProduct.value,
        }))}
        value={selectedProduct.value}
        onChangeValue={(value) => {
          const newParams: CommissionsSearchParams = {
            ...searchParams,
            inviteShareProduct: value,
          };

          router.push(`?${joinSearchParams(newParams)}`, {
            scroll: false,
          });
        }}
      />
      {displayedInvites.map((i) => (
        <div key={`invite-link-${i.value}-${i.product}`}>
          <div className="flex items-center justify-between rounded-[60px] font-bold border border-tonal-dark-cream-80 bg-[#fcfcfc] p-2 text-grey-blue max-w-[432px]">
            <p className="p-2 overflow-hidden text-ellipsis whitespace-nowrap">{i.value}</p>
            <Button variant="filled" color="yellow" size="iconXSmall" leadingIcon={<FileCopy />} />
          </div>
          <p className="text-tonal-dark-cream-40 text-sm pt-2">
            <b>{commissionProducts.find((p) => p.value === i.product)?.label}</b> Valid until:{" "}
            {intlDateFormatter.format(i.expiration).replaceAll("/", ".")}
          </p>
        </div>
      ))}
    </div>
  );
}
