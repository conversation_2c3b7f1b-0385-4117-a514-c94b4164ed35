import { Header } from "@/components/_common/header";
import { ProfileDropdown } from "@/components/_common/header/profile-dropdown";
import { BreadcrumbItems } from "@/components/ui/breadcrumb/breadcrumb";
import { PeopleAlt } from "@arthursenno/lizenzero-ui-react/Icon";

interface PartnerReviewHeaderProps {
  paths?: { href: string; label: string }[];
}

export function PartnerReviewHeader({ paths = [] }: PartnerReviewHeaderProps) {
  return (
    <>
      <div className="w-full px-4 border-b-[1px] border-tonal-dark-cream-80">
        <div className="max-w-7xl w-full mx-auto">
          <Header>
            <ProfileDropdown />
          </Header>
        </div>
      </div>
      <div className="w-full px-4 max-md:pb-14 py-6 md:py-10 bg-surface-03 relative">
        <div className="max-w-7xl mx-auto">
          <BreadcrumbItems paths={paths} />
        </div>
        <div className="absolute left-1/2 bottom-0 -translate-x-1/2 translate-y-[1px]">
          <svg xmlns="http://www.w3.org/2000/svg" width="138" height="41" viewBox="0 0 138 41" fill="none">
            <path
              d="M0 41C11.9572 41 22.7387 34.2961 28.5534 23.8482C36.9834 8.71243 53.1477 -0.0794577 68.2722 0.000541253C86.372 0.09654 100.201 9.83241 109.311 24.6562C115.477 34.6961 126.219 41 138 41"
              fill="#FCFCFC"
            />
          </svg>
          <PeopleAlt className="size-8 md:size-9 fill-primary absolute top-4 left-[51px]" />
        </div>
      </div>
    </>
  );
}
