"use client";

import { createCompany } from "@/lib/api/company";
import { createPartner } from "@/lib/api/partner";
import { useSession } from "next-auth/react";
import { enqueueSnackbar } from "notistack";
import { useFormContext } from "react-hook-form";

import { PartnerInformationCompanyForm } from "../../components/partner-information-form/partner-information-company";
import { PartnerInformationBankingForm } from "../../components/partner-information-form/partner-information-banking";
import { PartnerInformationSubmit } from "../../components/partner-information-form/partner-information-submit";
import { PartnerInformationFormData } from "../../components/partner-information-form/partner-information-provider";

export function ReviewInformationForm() {
  const session = useSession();

  const form = useFormContext<PartnerInformationFormData>();

  async function handleFormSubmit(data: PartnerInformationFormData) {
    const user = session.data?.user;

    if (!user) return;

    const createdPartner = await createPartner({
      banking: {
        international_account_number: data.internationalAccountNumber || undefined,
        business_identifier_code: data.businessIdentifierCode || undefined,
      },
      partner_firstname: user.first_name,
      partner_lastname: user.last_name,
      partner_email: user.email,
      user_id: Number(user.id),
    });

    if (!createdPartner) {
      enqueueSnackbar("Error when saving data", { variant: "error" });
      return;
    }

    const createdCompany = await createCompany({
      partner_id: createdPartner.id,
      name: data.companyName,
      description: data.descriptionOfBusiness || undefined,
      starting: data.startingDateOfCooperation ? new Date(data.startingDateOfCooperation).toISOString() : undefined,
      website: data.partnerOfferWebsite || undefined,
      contact: {
        name: data.contactName || "",
        email: data.contactEmail || "",
        phone_mobile: data.contactPhone || "",
      },
      address: {
        country_code: data.countryCode,
        address_line: data.addressLine,
        city: data.city,
        zip_code: data.zipCode,
        street_and_number: data.streetAndNumber,
        additional_address: data.additionalAddressLine || "",
      },
    });

    if (!createdCompany) {
      enqueueSnackbar("Error when saving data", { variant: "error" });
      return;
    }
  }

  return (
    <form id="partner-review-information-form" onSubmit={form.handleSubmit(handleFormSubmit)}>
      <div className="space-y-12">
        <PartnerInformationCompanyForm />
        <PartnerInformationBankingForm />
        <PartnerInformationSubmit />
      </div>
    </form>
  );
}
