"use client";

import { Divider } from "@/components/_common/divider";
import { JOURNEYS } from "@/utils/journeys";
import { useJourney } from "@/hooks/use-journey";
import { useShoppingCart } from "@/hooks/use-shopping-cart";
import { ContractType } from "@/lib/api/contracts/types";
import { cn } from "@/lib/utils";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Check, CheckCircleOutline } from "@arthursenno/lizenzero-ui-react/Icon";
import { useTranslations } from "next-intl";

interface InterestProductCardProps {
  type: ContractType | "WORKSHOP";
  disabled?: boolean;
  title: string;
  description: string;
  price: number;
  contains: string[];
  added?: boolean;
}

export function InterestProductCard({ type, title, description, price, contains, disabled }: InterestProductCardProps) {
  const { shoppingCart, updateCart } = useShoppingCart();
  const { redirectToJourney } = useJourney();
  const t = useTranslations("shop.common.journey.shoppingCart.interestProducts");

  const isAdded = shoppingCart.items.some((item) => item.service_type === type);

  async function handleAddToCart() {
    if (disabled) return;

    if (type === "WORKSHOP") return;

    const journeyType = type === "EU_LICENSE" ? "QUICK_LICENSE" : "QUICK_ACTION_GUIDE";

    redirectToJourney(journeyType);
  }

  return (
    <div>
      <div className={cn("p-6 border border-[#808FA9] rounded-3xl bg-white", isAdded ? "h-auto" : "h-full")}>
        <div className="flex items-center gap-6 justify-between">
          <div className="flex flex-col gap-1">
            <p className="text-primary text-2xl font-bold">{title}</p>
            <p className="font-medium text-xs text-[#808FA9]">{description}</p>
          </div>
          {isAdded ? (
            <div className="flex items-center gap-2 mt-1">
              <p className="text-on-surface-01 text-sm font-bold">{t("added")}</p>
              <Check className="size-4 fill-on-surface-01" />
            </div>
          ) : (
            <div className="flex flex-col justify-end gap-1 mt-1">
              <p className="text-[28px] font-bold text-support-blue text-right">{price} €</p>
              <p className="text-xs font-medium text-tonal-dark-cream-40">{t("perCountryAndYear")}</p>
            </div>
          )}
        </div>
        {!isAdded && (
          <>
            <Divider style={{ marginTop: "16px", marginBottom: "16px" }} />

            <p className="text-base font-bold text-tonal-dark-cream-20">{t("contains")}:</p>

            <div className="flex flex-col gap-5 w-2/3 mt-4">
              {contains.map((item, index) => (
                <div key={index} className="flex items-start gap-3">
                  <CheckCircleOutline className="size-4 fill-tonal-dark-cream-20 flex-none" />
                  <p className="text-sm text-tonal-dark-cream-20">{item}</p>
                </div>
              ))}
            </div>

            <div className="mt-4">
              <Button color="dark-blue" size="medium" variant="filled" disabled={disabled} onClick={handleAddToCart}>
                + {t("addToCart")}
              </Button>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
