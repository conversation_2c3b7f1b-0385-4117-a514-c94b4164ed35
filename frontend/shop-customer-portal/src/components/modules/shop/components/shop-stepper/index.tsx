"use client";
import { Stepper } from "@/components/ui/stepper";
import { useTranslations } from "next-intl";

interface ShopStepperProps {
  step: number;
}

// TODO: refactor this and add to layout
export function ShopLicenseStepper({ step }: ShopStepperProps) {
  const t = useTranslations("shop.common.stepper.license");
  return (
    <Stepper
      className="max-w-7xl mx-auto pt-8 md:pt-12"
      currentStep={step}
      steps={[
        { index: 1, title: t("volumeCalculator") },
        { index: 2, title: t("data") },
        { index: 3, title: t("payment") },
        { index: 4, title: t("checkAndOrder") },
      ]}
    />
  );
}

export function ShopActionStepper({ step }: ShopStepperProps) {
  const t = useTranslations("shop.common.stepper.actionGuide");
  return (
    <Stepper
      className="max-w-7xl mx-auto pt-8 md:pt-12"
      currentStep={step}
      steps={[
        { index: 1, title: t("shoppingCart") },
        { index: 2, title: t("data") },
        { index: 3, title: t("payment") },
        { index: 4, title: t("checkAndOrder") },
      ]}
    />
  );
}
