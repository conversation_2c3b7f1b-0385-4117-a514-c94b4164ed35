"use client";
import { IconBanner, ShopBanner } from "@arthursenno/lizenzero-ui-react/Banner";
import { ControllerPainel, Lightbulb, WorkspacePremium } from "@arthursenno/lizenzero-ui-react/Icon";
import { useTranslations } from "next-intl";

export function ShopInformationBanner() {
  const t = useTranslations("shop.common.journey.billing.bannerInformation");
  return (
    <ShopBanner title="" style={{ width: "100%" }}>
      <div className="flex flex-col gap-10">
        <div className="flex items-start">
          <IconBanner
            className="text-white "
            icon={() => <Lightbulb width={24} height={24} className="fill-tonal-dark-blue-80" />}
          />

          <div className="">
            <p className="font-bold text-base">{t("first.title")}</p>
            <span className="w-full text-sm ">{t("first.description")}</span>
          </div>
        </div>
        <div className="flex items-start">
          <IconBanner
            className="text-white "
            icon={() => <ControllerPainel width={24} height={24} className="fill-tonal-dark-blue-80" />}
          />

          <div className="">
            <p className="font-bold text-base">{t("second.title")}</p>
            <span className="w-full text-sm ">{t("second.description")}</span>
          </div>
        </div>
        <div className="flex items-start">
          <IconBanner
            className="text-white "
            icon={() => <WorkspacePremium width={24} height={24} className="fill-tonal-dark-blue-80" />}
          />

          <div className="">
            <p className="font-bold text-base">{t("third.title")}</p>
            <span className="w-full text-sm ">{t("third.description")}</span>
          </div>
        </div>
      </div>
    </ShopBanner>
  );
}
