import { ReactNode } from "react";
import { cn } from "@/lib/utils";

interface ShopContentProps {
  className?: string;
  containerClassName?: string;
  children: ReactNode;
}

export function ShopContent({ children, className, containerClassName }: ShopContentProps) {
  return (
    <div className={cn("px-4", containerClassName)}>
      <div className={cn("w-full max-w-7xl mx-auto py-8 md:py-20", className)}>{children}</div>
    </div>
  );
}
