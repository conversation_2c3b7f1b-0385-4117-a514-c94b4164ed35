"use client";

import { formatCurrency } from "@/utils/formatCurrency";
import {
  CheckBox,
  CheckBoxOutlineBlank,
  CheckCircleOutline,
  KeyboardArrowDown,
  KeyboardArrowUp,
} from "@arthursenno/lizenzero-ui-react/Icon";
import { useTranslations } from "next-intl";
import { useState } from "react";

interface ServiceCardProps {
  initialCollapsible?: boolean;
  mountsChecked?: boolean;
  title: string;
  subTitle: string;
  price: number;
  contains: string[];
  popular?: boolean;
  onCheck: (value: boolean) => void;
  checked: boolean;
}

export function ServiceCard({
  mountsChecked = false,
  title,
  subTitle,
  price,
  contains,
  popular = false,
  onCheck,
  checked,
}: ServiceCardProps) {
  const t = useTranslations("shop.longJourney.selectServices.cardService");
  const [isColappsed, setIsCollapsed] = useState(mountsChecked);

  const toggleCollapsed = () => {
    setIsCollapsed(!isColappsed);
  };

  return (
    <div className="px-4 py-6 md:p-6 w-full bg-white rounded-3xl border border-[#808FA9]">
      <div className="flex items-start md:items-center gap-4 justify-between">
        <label className="flex gap-6 items-start md:items-center cursor-pointer max-w-[60%]">
          {checked ? (
            <CheckBox className="fill-primary size-5 flex-none" />
          ) : (
            <CheckBoxOutlineBlank className="fill-on-tertiary size-5 flex-none" />
          )}

          <input type="checkbox" className="hidden" checked={checked} onClick={() => onCheck(!checked)} />

          <div className="flex flex-col gap-1">
            <p className="text-primary text-xl md:text-2xl font-bold">{title}</p>

            <p className="text-[#808FA9] font-medium text-xs">{subTitle}</p>
          </div>
        </label>
        <button className="flex gap-3 md:gap-6 items-center flex-none" onClick={toggleCollapsed}>
          <div>
            <div className="flex items-start gap-2">
              <p className="text-right w-20 md:w-auto mt-1 text-xs font-medium text-tonal-dark-cream-40">{t("from")}</p>
              <p className="text-2xl md:text-3xl text-support-blue font-bold text-right">{formatCurrency(price)}</p>
            </div>
            <p className="text-right w-20 md:w-auto mt-1 text-xs font-medium text-tonal-dark-cream-40">
              / {t("perCountryAndYear")}
            </p>
          </div>
          {isColappsed ? (
            <KeyboardArrowUp className="size-8 fill-support-blue" />
          ) : (
            <KeyboardArrowDown className="size-8 fill-support-blue" />
          )}
        </button>
      </div>
      {isColappsed && (
        <div className="mt-4 relative">
          <p className="text-base font-bold text-tonal-dark-cream-20">{t("contains")}:</p>
          <div className="mt-4 flex flex-col gap-5">
            {contains.map((conter, idx) => (
              <div className="flex items-start gap-3" key={idx}>
                <CheckCircleOutline className="size-4 fill-tonal-dark-cream-20 flex-none" />
                <p className="text-tonal-dark-cream-20 text-sm">{conter}</p>
              </div>
            ))}
          </div>
          {popular && (
            <div
              className="w-20 h-[81px] flex items-center justify-normal absolute bottom-0 right-0"
              style={{
                backgroundImage: "url(/assets/images/background.png)",
                backgroundPosition: "center",
                backgroundSize: "cover",
                backgroundRepeat: "no-repeat",
              }}
            >
              <p className="text-primary text-xs font-bold text-center">{t("mostPopular")}</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
