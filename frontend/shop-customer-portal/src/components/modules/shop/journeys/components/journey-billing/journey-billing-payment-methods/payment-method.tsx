import { TooltipIcon } from "@/components/_common/tooltipIcon";
import { PaymentMethodType } from "@/lib/api/payment/types";
import { cn } from "@/lib/utils";
import { RadioSelected, RadioUnselected } from "@arthursenno/lizenzero-ui-react/Icon";

interface PaymentMethodProps {
  label: string;
  icons: string[];
  borderLess?: boolean;
  paymentMethod?: PaymentMethodType;
  handleSetPaymentMethod?: (type: PaymentMethodType) => void;
  value: any;
  paymentMethodId?: string;
}

export function PaymentMethod({
  label,
  icons,
  borderLess,
  paymentMethod,
  handleSetPaymentMethod,
  value,
  paymentMethodId,
}: PaymentMethodProps) {
  return (
    <div
      onClick={() => handleSetPaymentMethod?.(value)}
      className={cn("flex flex-row gap-3 items-center p-5 cursor-pointer", {
        "border-t-2 border-t-surface-01": !borderLess,
      })}
    >
      {paymentMethod === value && !paymentMethodId ? (
        <RadioSelected width={16} className="fill-primary" />
      ) : (
        <RadioUnselected width={16} className="fill-tonal-dark-cream-60" />
      )}
      <div className="flex items-center gap-1 py-1">
        {icons.map((icon, index) => (
          <div
            key={`${icon}-${index}`}
            className="w-10 h-7 py-1 bg-tonal-dark-cream-96 rounded-sm flex items-center justify-center relative"
          >
            {/* eslint-disable-next-line @next/next/no-img-element */}
            <img src={`/assets/payment-methods/${icon}`} alt={icon} className="h-full w-auto object-contain" />
          </div>
        ))}
      </div>
      <p className="text-grey-blue text-base truncate">{label}</p>
      <TooltipIcon info={label} sizeIcon={20} />
    </div>
  );
}
