"use client";

import { Checkbox } from "@/components/_common/checkbox";
import { Divider } from "@/components/_common/divider";
import { CreateAccountSchema } from "@/components/_common/forms/schemas";
import { FormInputIcon } from "@/components/_common/input-status-icon";
import { LoginModal } from "@/components/_common/modals/login-modal";
import { PasswordStrengthBar } from "@/components/_common/password-strength-bar";
import { QuestionTooltip, QuestionTooltipDescription } from "@/components/_common/question-tooltip";
import { PasswordInput } from "@/components/ui/password-input";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { useShoppingCart } from "@/hooks/use-shopping-cart";
import { createAccount } from "@/lib/api/account";
import { TypeResendToken } from "@/lib/api/account/types";
import { getConsentsByType } from "@/lib/api/consent";
import { Icon<PERSON><PERSON><PERSON>, ShopBanner } from "@arthursenno/lizenzero-ui-react/Banner";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { East, Lightbulb } from "@arthursenno/lizenzero-ui-react/Icon";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { zodResolver } from "@hookform/resolvers/zod";
import { useQuery } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import { enqueueSnackbar } from "notistack";
import { useState } from "react";
import { Controller, useForm, useWatch } from "react-hook-form";
import { z } from "zod";
import { JourneyAccountVerificationModal } from "../journey-account-verification-modal";

export function JourneyCreateAccount() {
  const { shoppingCart } = useShoppingCart();
  const { changeParam } = useQueryFilter(["verify-account"]);
  const t = useTranslations("shop.common.journey.createAccount");
  const globalT = useTranslations("global");

  const verificationMode = shoppingCart.journey === "LONG" ? "MAGIC-LINK" : "CODE";

  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);

  const createAccountSchema = CreateAccountSchema.extend({
    enablePassword: z.boolean().default(false),
    consents: z.array(
      z.object({
        id: z.number(),
        name: z.string(),
        description: z.string(),
        isChecked: z.boolean(),
      })
    ),
  }).superRefine((data, ctx) => {
    if (data.enablePassword) {
      if (!data.password) {
        ctx.addIssue({ path: ["password"], code: z.ZodIssueCode.custom, message: globalT("validation.required") });
      }

      if (!data.confirmPassword) {
        ctx.addIssue({
          path: ["confirmPassword"],
          code: z.ZodIssueCode.custom,
          message: globalT("validation.required"),
        });
      }

      if (data.password !== data.confirmPassword) {
        ctx.addIssue({
          path: ["confirmPassword"],
          code: z.ZodIssueCode.custom,
          message: t("form.matchPassword"),
        });
      }
    }

    const consents = Object.values(data.consents || {});

    if (!!consents.length && consents.some((consent) => !consent.isChecked)) {
      ctx.addIssue({ path: ["consents"], code: z.ZodIssueCode.custom, message: t("form.consent.allChecked") });
    }
  });

  type CreateAccountFormData = z.infer<typeof createAccountSchema>;

  const consentListQuery = useQuery({
    queryKey: ["consent-list", "account"],
    queryFn: async () => {
      const consents = await getConsentsByType("ACCOUNT");

      if (!consents) throw new Error("Unable to fetch consent list");

      form.setValue(
        "consents",
        consents.map((consent) => ({
          id: consent.id,
          name: consent.name,
          description: consent.description,
          isChecked: false,
        }))
      );

      return consents;
    },
  });

  const form = useForm<CreateAccountFormData>({
    resolver: zodResolver(createAccountSchema),
    mode: "all",
    defaultValues: {
      consents: [],
    },
  });

  async function handleFormSubmit(data: CreateAccountFormData) {
    try {
      const createAccountResponse: any = await createAccount({
        email: data.email,
        first_name: data.firstName,
        last_name: data.lastName,
        company_name: data.companyName,
        password: data.password,
      });

      if (!createAccountResponse) throw new Error();

      if ([400, 409].includes(createAccountResponse?.response?.status)) {
        enqueueSnackbar(t("errors.emailInUse"), { variant: "error" });
        const errorMessage = createAccountResponse?.response?.data?.message;

        // TODO: i18n
        if (errorMessage !== `Check your email and confirm your account before continuing.`) {
          form.setError("email", {
            message: t("errors.emailInUse"),
            type: "already-exists",
          });
        }

        enqueueSnackbar(errorMessage || "Error creating user", { variant: "error" });

        return;
      }

      if (!createAccountResponse?.data) throw new Error();

      changeParam("verify-account", "true", { scroll: false });

      enqueueSnackbar(t("success"), { variant: "success" });
    } catch (err) {
      enqueueSnackbar(t("failed"), { variant: "error" });
    }
  }

  const email = useWatch({ control: form.control, name: "email" });
  const isPasswordEnabled = useWatch({ control: form.control, name: "enablePassword" });
  const password = useWatch({ control: form.control, name: "password" });
  const confirmPassword = useWatch({ control: form.control, name: "confirmPassword" });
  const consents = useWatch({ control: form.control, name: "consents" });
  const isNotValidConfirmPassword = password && confirmPassword && password !== confirmPassword;
  const errors = form.formState.errors;

  return (
    <>
      <div className="w-full grid grid-cols-1 md:grid-cols-12 gap-6 justify-center mb-20">
        <form className="w-full md:col-span-7" onSubmit={form.handleSubmit(handleFormSubmit)}>
          <div className="col-span-7 w-full">
            <div className="w-full rounded-[32px] items-start bg-surface-01 flex flex-col py-7 px-4 md:px-8">
              <p className="text-[#808FA9] font-light text-sm mb-8">*{t("mandatoryFields")}</p>
              <div className="w-full ">
                <Input
                  {...form.register("email")}
                  label={t("form.email.label")}
                  placeholder={t("form.email.placeholder")}
                  variant={errors.email && "error"}
                  errorMessage={
                    errors.email &&
                    (errors.email.type === "already-exists" ? (
                      <p className="flex gap-1">
                        {errors.email.message}.
                        <span className="font-bold underline cursor-pointer" onClick={() => setIsLoginModalOpen(true)}>
                          {t("form.email.login")}.
                        </span>
                        <QuestionTooltip className="fill-error hover:fill-on-error-container">
                          <QuestionTooltipDescription>{t("form.email.tooltip")}</QuestionTooltipDescription>
                        </QuestionTooltip>
                      </p>
                    ) : (
                      errors.email.message
                    ))
                  }
                  rightIcon={<FormInputIcon control={form.control} name="email" />}
                  enabled={!form.formState.isSubmitting}
                />
              </div>
              <Divider />
              <div className="grid md:grid-cols-2 w-full gap-6 md:gap-8">
                <Input
                  {...form.register("firstName")}
                  label={t("form.firstName.label")}
                  placeholder={t("form.firstName.placeholder")}
                  variant={errors.firstName && "error"}
                  errorMessage={errors.firstName && errors.firstName.message}
                  rightIcon={<FormInputIcon control={form.control} name="firstName" />}
                  enabled={!form.formState.isSubmitting}
                />
                <Input
                  {...form.register("lastName")}
                  label={t("form.lastName.label")}
                  placeholder={t("form.lastName.placeholder")}
                  variant={errors.lastName && "error"}
                  errorMessage={errors.lastName && errors.lastName.message}
                  rightIcon={<FormInputIcon control={form.control} name="lastName" />}
                  enabled={!form.formState.isSubmitting}
                />
                <Input
                  label="Company Name"
                  placeholder="Company Name"
                  variant={errors.companyName && "error"}
                  errorMessage={errors.companyName && errors.companyName.message}
                  rightIcon={<FormInputIcon control={form.control} name="companyName" />}
                  enabled={!form.formState.isSubmitting}
                  {...form.register("companyName")}
                />
              </div>

              {shoppingCart.journey === "LONG" && (
                <>
                  <Divider />
                  <div className="space-y-4">
                    <Controller
                      control={form.control}
                      name="enablePassword"
                      disabled={form.formState.isSubmitting}
                      render={({ field }) => (
                        <Checkbox
                          label={t("form.createAccount.label")}
                          description={t("form.createAccount.description")}
                          checked={field.value}
                          onChange={(e) => field.onChange(e.target.checked)}
                          disabled={form.formState.isSubmitting}
                        />
                      )}
                    />
                    {isPasswordEnabled && (
                      <div className="space-y-5 w-full gap-8">
                        <div className="space-y-2">
                          <PasswordInput
                            {...form.register("password")}
                            label={t("form.password.placeholder")}
                            placeholder={t("form.password.placeholder")}
                            enabled={!form.formState.isSubmitting}
                            variant={(errors.confirmPassword || isNotValidConfirmPassword) && "error"}
                            errorMessage={errors.password && errors.password.message}
                          />
                          <PasswordStrengthBar password={password} />
                        </div>
                        <PasswordInput
                          {...form.register("confirmPassword")}
                          label={t("form.confirmPassword.placeholder")}
                          placeholder={t("form.confirmPassword.placeholder")}
                          enabled={!form.formState.isSubmitting}
                          variant={(errors.confirmPassword || isNotValidConfirmPassword) && "error"}
                          errorMessage={
                            (errors.confirmPassword && errors.confirmPassword.message) ||
                            (isNotValidConfirmPassword && t("form.passwordMismatch"))
                          }
                        />
                      </div>
                    )}
                  </div>
                </>
              )}
              <Divider />
              <div className="space-y-4">
                {!!errors.consents && <p className="text-tonal-red-40 text-sm">{errors.consents.root?.message}</p>}
                {consents.map((consent, consentIndex) => (
                  <Controller
                    key={consent.id}
                    control={form.control}
                    disabled={form.formState.isSubmitting}
                    name={`consents.${consentIndex}`}
                    render={({ field }) => (
                      <Checkbox
                        label={consent?.name || ""}
                        description={consent?.description}
                        checked={field.value?.isChecked}
                        onChange={(e) => field.onChange({ ...field.value, isChecked: e.target.checked })}
                        disabled={form.formState.isSubmitting}
                      />
                    )}
                  />
                ))}
              </div>
              <div className="flex flex-col md:flex-row md:items-center md:justify-end gap-4 w-full mt-5">
                <Button
                  color="dark-blue"
                  variant="outlined"
                  size="medium"
                  onClick={() => setIsLoginModalOpen(true)}
                  type="button"
                  disabled={form.formState.isSubmitting}
                  style={{ textWrap: "nowrap" }}
                  className="w-full md:w-auto"
                >
                  {t("form.alreadyHaveAccount")}
                </Button>
                <Button
                  color={!!Object.keys(errors).length ? "red" : "yellow"}
                  variant="filled"
                  size="medium"
                  disabled={consentListQuery.isLoading || form.formState.isSubmitting}
                  trailingIcon={!form.formState.isSubmitting && <East />}
                  className="w-full md:w-auto"
                >
                  {form.formState.isSubmitting ? t("form.button.loading") : t("form.button.continue")}
                </Button>
              </div>
            </div>
          </div>
        </form>
        <div className="w-full md:col-span-5">
          <ShopBanner className="!w-full !h-full md:!min-h-[20rem]" title="">
            <IconBanner
              className="text-white items-start flex"
              style={{ alignItems: "flex-start" }}
              icon={() => <Lightbulb width={24} height={24} className="fill-[#A9C8FF]" />}
            />

            <div className="">
              <p className="font-bold text-base">{t("banner.title")}</p>
              <span className="w-full text-sm ">{t("banner.subtitle")}</span>
              <p className="font-light text-base mt-6">{t("banner.description")}</p>
            </div>
          </ShopBanner>
        </div>
      </div>
      <LoginModal key={email} defaultEmail={email} open={isLoginModalOpen} onClose={() => setIsLoginModalOpen(false)} />
      <JourneyAccountVerificationModal
        type={verificationMode}
        resendTokenType={TypeResendToken.CREATE_ACCOUNT}
        email={email!}
      />
    </>
  );
}
