"use client";

import { CountryIcon } from "@/components/_common/country-icon";
import { Divider } from "@/components/_common/divider";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import InputVoucher from "@/components/ui/input-voucher";
import { useCustomer } from "@/hooks/use-customer";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { useShoppingCart } from "@/hooks/use-shopping-cart";
import { useRouter } from "@/i18n/navigation";
import { createManyCustomerConsents, getConsentsByType } from "@/lib/api/consent";
import { ConsentListItem } from "@/lib/api/consent/types";
import { BaseCountry } from "@/lib/api/country/types";
import { createCheckoutSession, createPaymentMethod, getCheckoutSession } from "@/lib/api/payment";
import { PaymentMethodType } from "@/lib/api/payment/types";
import { createPurchase } from "@/lib/api/purchase";
import { getPurchasedShoppingCartByEmail } from "@/lib/api/shoppingCart";
import { ShoppingCartItem } from "@/lib/api/shoppingCart/types";
import { queryClient } from "@/lib/react-query";
import { formatWeight } from "@/utils/format-weight";
import { formatCurrency } from "@/utils/formatCurrency";
import { JourneyType } from "@/utils/journeys";
import { STRIPE_CHECKOUT_PAYMENT_METHODS } from "@/utils/system-consts";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Clear, East, KeyboardArrowUp } from "@arthursenno/lizenzero-ui-react/Icon";
import { useStripe } from "@stripe/react-stripe-js";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import { enqueueSnackbar } from "notistack";
import { ChangeEvent, useEffect, useState } from "react";
import { JourneyThirdParty } from "../journey-third-party";
import { JourneyPurchaseConfirmationModal } from "./journey-purchase-confirmation-modal";
import { JourneyPurchaseInformation } from "./journey-purchase-information";
import { JourneyPurchasePaymentMethod } from "./journey-purchase-payment-method";
import { Checkbox } from "@/components/_common/checkbox";

interface JourneyPurchaseProps {
  journey: JourneyType;
}

export function JourneyPurchase({ journey }: JourneyPurchaseProps) {
  const { shoppingCart, updateCart, updateQueryData } = useShoppingCart();

  const t = useTranslations("shop.common.journey.purchase");
  const tCoupon = useTranslations("shop.common.journey.shoppingCart.coupon");

  const router = useRouter();
  const stripe = useStripe();

  const { customer } = useCustomer();

  const { paramValues } = useQueryFilter(["initial-coupon", "session_id"]);

  const initialCoupon = paramValues["initial-coupon"];
  const sessionId = paramValues["session_id"];

  const [isLoadingPurchase, setIsLoadingPurchase] = useState(false);

  const [showInputDiscountCode, setShowInputDiscountCode] = useState(false);

  const [coupon, setCoupon] = useState("");
  const [loadingCoupon, setloadingCoupon] = useState(false);
  const [errorMsgCoupon, setErrorMsgCoupon] = useState("");

  const [consentList, setConsentList] = useState<ConsentListItem[]>([]);

  const isNewPurchase = !customer?.contracts?.length;

  const removeCountryMutation = useMutation({
    mutationFn: async (countryCode: string) => {
      await updateCart({
        items: shoppingCart.items.filter((i) => i.country_code !== countryCode),
      });
    },
  });

  const consentListQuery = useQuery({
    queryKey: ["consent-list", "purchase"],
    queryFn: async () => {
      try {
        const response = await getConsentsByType("PURCHASE");

        if (!response) throw new Error("Unable to fetch consent list");

        setConsentList(response.map((consent) => ({ ...consent, given: !isNewPurchase })));
      } catch (err) {
        enqueueSnackbar(t("consentList.errors.fetch"), { variant: "error" });
      }
    },
    enabled: !!customer,
  });

  const isAllConsentsGiven = consentList.every((consent) => consent.given);

  function handleChangeCoupon(event: ChangeEvent<HTMLInputElement>) {
    setErrorMsgCoupon("");
    setCoupon(event.target.value);
  }

  async function handleCoupon(initialCode?: string) {
    setShowInputDiscountCode(true);

    if (!coupon && !initialCode) return;

    setloadingCoupon(true);

    try {
      setCoupon("");

      await updateCart({
        coupon,
        coupon_type: `WRITTEN`,
      });

      if (initialCode && typeof initialCode === "string") {
        setCoupon(initialCode);
      }
      enqueueSnackbar(t("coupon.success"), { variant: "success" });
    } catch {
      setErrorMsgCoupon(t("coupon.errors.invalid"));

      enqueueSnackbar(t("coupon.errors.unableToApply"), { variant: "error" });
    } finally {
      setloadingCoupon(false);
    }
  }

  async function handleRemoveCoupon() {
    await updateCart({ coupon: null });
  }

  function handleClickAddCode() {
    setShowInputDiscountCode(true);
  }

  useEffect(() => {
    if (sessionId) {
      handleCheckoutSession();
      return;
    }

    if (initialCoupon) {
      handleCoupon(initialCoupon);
    }
  }, []);

  async function handlePurchase() {
    try {
      if (!customer) return;

      setIsLoadingPurchase(true);

      const paymentMethodType = shoppingCart.payment?.payment_method_type;

      if (!stripe) throw new Error(t("errors.retrieveStripe"));

      if (!paymentMethodType) throw new Error(t("errors.retrievePaymentMethodType"));

      if (consentList.some((consent) => !consent.given)) {
        enqueueSnackbar("Please check all the consents", { variant: "error" });
        return;
      }

      const customerConsentData = consentList.map((consent) => ({
        customer_id: customer.id,
        consent_id: consent.id,
        given: consent.given,
      }));
      const customerConsents = await createManyCustomerConsents(customerConsentData);
      if (!customerConsents) throw new Error("Error creating customer consents");

      // Handle payment with stripe checkout payment methods
      if (STRIPE_CHECKOUT_PAYMENT_METHODS.includes(paymentMethodType || "") && paymentMethodType) {
        const sessionId = await createCheckoutSession({
          customer_id: customer.id,
          total: shoppingCart.total,
          payment_method_type: paymentMethodType,
          currency: customer.currency || "EUR",
          journey: shoppingCart.journey,
        });

        if (!sessionId) throw new Error("Error creating checkout session");

        const { error } = await stripe.redirectToCheckout({ sessionId });

        if (error) {
          console.log("Checkout error:", error.message);
          throw new Error("Checkout error, check console");
        }

        return;
      }

      const paymentResponse = await createPurchase({ shopping_cart_id: shoppingCart.id });

      if (!paymentResponse.success) throw new Error("Error creating payment");

      const orderId = String(paymentResponse.data.order_id);

      await updateCart({ payment: { ...shoppingCart.payment, order_id: Number(orderId) } });

      const purchasedCart = await getPurchasedShoppingCartByEmail(customer.email);

      if (!purchasedCart) return;

      updateQueryData({ ...purchasedCart, items: [] });
      queryClient.invalidateQueries({
        predicate: (query) => {
          return (
            query.queryKey.includes("customer") ||
            query.queryKey.includes("contract") ||
            query.queryKey.includes("license") ||
            query.queryKey.includes("packaging-service") ||
            query.queryKey.includes("order")
          );
        },
      });

      router.push(`./conclusion?order_id=${orderId}`);
    } catch (err: any) {
      setIsLoadingPurchase(false);

      enqueueSnackbar(err?.response?.data?.message || t("errors.failedToCreateOrder"), { variant: "error" });
    }
  }

  async function handleCheckoutSession() {
    try {
      if (!customer) return;

      setIsLoadingPurchase(true);

      if (!customer) throw new Error("Error retrieving customer");

      if (!sessionId) throw new Error("Error retrieving stripe session id");
      const stripeSession = await getCheckoutSession(sessionId);

      if (!stripeSession) throw new Error("Failed to fetch stripe session");
      if (!customer.email) throw new Error("Error retrieving user email");
      if (!shoppingCart) throw new Error("Error retrieving shopping cart");

      const paymentMethodType = stripeSession.payment_method_types[0].toUpperCase() as PaymentMethodType;
      const paymentIntentId = stripeSession.payment_intent.id;

      const paymentMethodResponse = await createPaymentMethod({
        customer_id: customer.id,
        platform: "stripe",
        platform_payment_method_id: stripeSession.payment_intent.payment_method as string,
        type: paymentMethodType,
        saved_for_future_purchase: false,
      });

      if (!paymentMethodResponse.success) throw new Error("Failed to create payment method");

      const paymentMethod = paymentMethodResponse.data;

      await updateCart({
        payment: {
          ...shoppingCart.payment,
          payment_method_id: paymentMethod.id,
          payment_method_type: paymentMethod.type,
          platform_payment_id: paymentIntentId,
        },
      });

      const paymentResponse = await createPurchase({ shopping_cart_id: shoppingCart.id });

      if (!paymentResponse.success) throw new Error("Failed to create payment");

      const orderId = String(paymentResponse.data.order_id);

      await updateCart({ payment: { ...shoppingCart.payment, order_id: Number(orderId) } });

      const purchasedCart = await getPurchasedShoppingCartByEmail(customer.email);

      if (!purchasedCart) return;

      updateQueryData({ ...purchasedCart, items: [] });
      queryClient.invalidateQueries({
        predicate: (query) => {
          return (
            query.queryKey.includes("customer") ||
            query.queryKey.includes("contract") ||
            query.queryKey.includes("license") ||
            query.queryKey.includes("packaging-service") ||
            query.queryKey.includes("order")
          );
        },
      });

      router.push(`./conclusion?order_id=${orderId}`);
    } catch (err: any) {
      setIsLoadingPurchase(false);
      enqueueSnackbar("Failed to complete purchase. Please, try again.", { variant: "error" });
    }
  }

  function handleReconfigureProduct() {
    if (journey === "QUICK_ACTION_GUIDE") {
      router.push("./shopping-cart");
      return;
    }

    if (journey === "LONG") {
      router.push("./select-countries");
      return;
    }

    router.push("./calculator");
  }

  function handleToggleConsent(consentId: number) {
    setConsentList((prev) =>
      prev.map((consent) => {
        if (consent.id !== consentId) return consent;

        return { ...consent, given: !consent.given };
      })
    );
  }

  const purchaseItems = shoppingCart.items.reduce(
    (acc, item) => {
      const exists = acc.find((c) => c.country.code === item.country_code);

      if (!exists) {
        acc.push({
          country: {
            id: item.country_id,
            code: item.country_code,
            name: item.country_name,
            flag_url: item.country_flag,
          },
          euLicense: item.service_type === "EU_LICENSE" ? item : null,
          actionGuide: item.service_type === "ACTION_GUIDE" ? item : null,
          directLicense: item.service_type === "DIRECT_LICENSE" ? item : null,
        });

        return acc;
      }

      if (item.service_type === "EU_LICENSE") exists.euLicense = item;
      if (item.service_type === "ACTION_GUIDE") exists.actionGuide = item;
      if (item.service_type === "DIRECT_LICENSE") exists.directLicense = item;

      return acc;
    },
    [] as {
      country: BaseCountry;
      euLicense: ShoppingCartItem | null;
      actionGuide: ShoppingCartItem | null;
      directLicense: ShoppingCartItem | null;
    }[]
  );

  const isPurchaseDisabled =
    !shoppingCart.items.length || isLoadingPurchase || consentListQuery.isLoading || !isAllConsentsGiven;

  return (
    <>
      <div className="w-full min-h-screen flex flex-col md:flex-row gap-6 justify-center mb-5">
        <div className="w-full md:w-4/6">
          <div className="bg-tonal-cream-96 w-full rounded-[32px] px-4 py-6 md:py-10 md:pr-10 md:pl-7">
            <div className="flex flex-col gap-6 mb-6">
              <p className="text-primary text-2xl font-bold">{t("resume")}</p>
              <div className="flex justify-between items-center">
                <p className="text-primary text-xl font-bold">
                  {journey === "QUICK_ACTION_GUIDE" ? t("titles.actionGuide") : t("titles.license")}*
                </p>
                <button className="text-support-blue text-sm font-bold" onClick={handleReconfigureProduct}>
                  {t("reconfigure")}
                </button>
              </div>
              <p className="text-primary text-base">{t("contractDuration")}</p>
            </div>

            <div className="flex flex-col gap-6">
              {purchaseItems.map((item) => (
                <div key={item.country.code} className="bg-background px-4 pt-3 pb-6 rounded-2xl">
                  <div className="flex justify-between items-center py-4">
                    <div className="flex items-center gap-3">
                      <CountryIcon
                        country={{ name: item.country.name, flag_url: item.country.flag_url }}
                        className="size-7"
                      />
                      <p className="text-primary text-xl font-bold mt-2">{item.country.name}</p>
                    </div>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="text" color="light-blue" size="small">
                          Remove country
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Remove country?</AlertDialogTitle>
                          <AlertDialogDescription>
                            By clicking on ”confirm” you are removing this country and all its products from your
                            shopping cart.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel
                            disabled={
                              removeCountryMutation.variables === item.country.code && removeCountryMutation.isPending
                            }
                          >
                            Cancel
                          </AlertDialogCancel>
                          <AlertDialogAction
                            asChild
                            disabled={
                              removeCountryMutation.variables === item.country.code && removeCountryMutation.isPending
                            }
                            onClick={(e) => {
                              e.preventDefault();
                              removeCountryMutation.mutate(item.country.code);
                            }}
                          >
                            {removeCountryMutation.variables === item.country.code && removeCountryMutation.isPending
                              ? "Removing..."
                              : "Remove"}
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>

                  <Divider style={{ margin: 0 }} />
                  {item.euLicense && (
                    <>
                      <div className="flex items-center justify-between py-4">
                        <p className="text-primary text-base">Registration Fee</p>
                        <p className="text-primary text-base font-bold">
                          {formatCurrency(item.euLicense.price_list?.registration_fee || 0)}
                        </p>
                      </div>
                      <Divider style={{ margin: 0, marginBottom: 0 }} />
                      <div className="flex items-center justify-between py-4">
                        <p className="text-primary text-base">Handling Fee</p>
                        <p className="text-primary text-base font-bold">
                          {formatCurrency(item.euLicense.price_list?.handling_fee || 0)}
                        </p>
                      </div>
                    </>
                  )}
                  {item.actionGuide && (
                    <>
                      <Divider style={{ margin: 0, marginBottom: 0 }} />
                      <div className="flex items-center justify-between py-4">
                        <p className="text-primary text-base">Action Guide</p>
                        <p className="text-primary text-base font-bold">
                          {formatCurrency(item.actionGuide.price_list?.price || 0)}
                        </p>
                      </div>
                    </>
                  )}
                  {item.directLicense && (
                    <>
                      <Divider style={{ margin: 0, marginBottom: 0 }} />
                      <div className="flex items-center justify-between py-4">
                        <p className="text-primary text-base">Direct License</p>
                        <p className="text-primary text-base font-bold">{formatCurrency(item.directLicense.price)}</p>
                      </div>
                    </>
                  )}
                  {item.directLicense?.packaging_services &&
                    !!Object.values(item.directLicense.packaging_services).length && (
                      <div className="mt-4 space-y-3">
                        {Object.values(item.directLicense.packaging_services).map((packagingService) => (
                          <details key={packagingService.id} className="group">
                            <summary className="flex gap-4 text-tonal-dark-cream-20 font-bold items-center">
                              {packagingService.name}
                              <KeyboardArrowUp
                                width={24}
                                height={24}
                                className="chevron group-open:rotate-180 transition-all duration-300 fill-support-blue w-7 h-7"
                              />
                            </summary>
                            {Object.entries(packagingService.fractions).map(([fractionCode, fraction]) => (
                              <div key={`${packagingService.id}_${fractionCode}`}>
                                <div className="py-4 px-3 flex justify-between items-center">
                                  <p className="text-base text-tonal-dark-cream-20">{fraction.name}</p>
                                  <p className="text-base text-tonal-dark-cream-20">{formatWeight(fraction.weight)}</p>
                                </div>

                                <Divider style={{ margin: 0 }} />
                              </div>
                            ))}
                          </details>
                        ))}
                      </div>
                    )}
                </div>
              ))}
            </div>

            {["long", "quick_license"].includes(journey) && <JourneyThirdParty className="bg-surface-03 mt-4" />}

            {shoppingCart.coupon ? (
              <div className="flex p-4 my-7 justify-between items-center">
                <div className="flex gap-2">
                  <Clear className="fill-support-blue size-5 cursor-pointer" onClick={handleRemoveCoupon} />
                  <p className="text-sm text-tonal-dark-green-30">{shoppingCart.coupon.code}</p>
                </div>
                <p className="text-sm text-tonal-dark-green-30 font-bold">
                  -{" "}
                  {shoppingCart.coupon.discount_type === "ABSOLUTE"
                    ? formatCurrency(shoppingCart.coupon.value)
                    : `${shoppingCart.coupon.value} %`}
                </p>
              </div>
            ) : (
              <div className="p-4 flex justify-between items-center my-7">
                <p className="text-base text-tonal-dark-cream-30">Discount Code</p>
                {showInputDiscountCode ? (
                  <InputVoucher
                    onChange={handleChangeCoupon}
                    value={coupon}
                    errorMessage={errorMsgCoupon}
                    onBlur={handleCoupon}
                    onClick={handleCoupon}
                    loading={loadingCoupon}
                    placeholder={tCoupon("placeholder")}
                  />
                ) : (
                  <button onClick={handleClickAddCode} className="text-support-blue text-base">
                    {t("coupon.button.label")}
                  </button>
                )}
              </div>
            )}

            <div>
              {shoppingCart.vat_value ? (
                <div className="py-3 px-4 bg-background rounded-t-2xl border-2 border-surface-03">
                  <div className="py-4 flex justify-between items-center">
                    <p className="text-base text-primary">VAT {shoppingCart?.vat_percentage}%</p>
                    <p className="text-base text-primary font-bold">{formatCurrency(shoppingCart?.vat_value)}</p>
                  </div>
                </div>
              ) : null}
              <div className="p-4 rounded-b-2xl bg-[#CAE5EC]">
                <div className="flex justify-between items-center">
                  <p className="text-xl text-primary font-bold">{t("total")}</p>
                  <p className="text-2xl text-primary font-bold">{formatCurrency(shoppingCart?.total)}</p>
                </div>
              </div>
            </div>

            <p className="italic text-sm text-on-surface-01 mt-6">{t("handlingFee")}</p>
          </div>
        </div>

        <div className="w-full md:w-2/6">
          <JourneyPurchasePaymentMethod
            paymentMethodId={shoppingCart.payment?.payment_method_id}
            paymentMethodType={shoppingCart.payment?.payment_method_type}
            usePersonalData={true}
          />
          <JourneyPurchaseInformation />
        </div>
      </div>

      <div className="mt-14 mb-36">
        <Divider style={{ margin: 0 }} />

        <p className="py-10 text-primary font-bold text-[28px]">{t("conditionsDataProtection")}</p>

        <div className="flex flex-col gap-3">
          {consentList.map((consent) => (
            <Checkbox
              key={consent.id}
              label={consent.description}
              checked={consent.given}
              onClick={() => handleToggleConsent(consent.id)}
            />
          ))}
        </div>

        <p className="text-sm text-tonal-dark-cream-40 mt-16">
          <span dangerouslySetInnerHTML={{ __html: t.raw("emailUsage") }} />
        </p>

        <p className="text-sm text-tonal-dark-cream-40 mt-4">{t("concludeContract")}</p>

        <div className="flex md:justify-end mt-10">
          <div className="w-full md:w-2/6 flex flex-col justify-center items-center">
            {!shoppingCart.items.length && <p className="text-base text-error my-5">{t("errors.selectCountry")}</p>}
            <Button
              trailingIcon={<East />}
              color="yellow"
              variant="filled"
              size="medium"
              onClick={handlePurchase}
              disabled={isPurchaseDisabled}
            >
              {isNewPurchase ? t("button.newPurchase") : t("button.finalizePurchase")}
            </Button>
          </div>
        </div>
      </div>
      {isLoadingPurchase && <JourneyPurchaseConfirmationModal />}
    </>
  );
}
