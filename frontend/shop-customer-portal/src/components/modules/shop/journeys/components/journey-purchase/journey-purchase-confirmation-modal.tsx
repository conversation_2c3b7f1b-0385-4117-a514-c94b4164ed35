import { Spinner } from "@/components/ui/loader";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import { useTranslations } from "next-intl";

export function JourneyPurchaseConfirmationModal() {
  const t = useTranslations("shop.common.creatingPurchase");

  return (
    <Modal open={true} className="z-50 w-full" style={{ maxWidth: "400px", borderRadius: "52px" }}>
      <div className="p-5">
        <div className="text-primary">
          <div className="flex flex-col items-center justify-center gap-4">
            <Spinner size="md" />
            <p className="text-center">{t("description")}</p>
          </div>
        </div>
      </div>
    </Modal>
  );
}
