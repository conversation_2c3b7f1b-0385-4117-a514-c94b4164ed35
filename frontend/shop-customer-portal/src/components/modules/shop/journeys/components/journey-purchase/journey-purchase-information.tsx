"use client";

import { useRouter } from "@/i18n/navigation";

import { useCustomer } from "@/hooks/use-customer";
import { COUNTRIES } from "@/utils/consts/countries";
import { useTranslations } from "next-intl";

import { useQuery } from "@tanstack/react-query";
import { getCompany } from "@/lib/api/company";

export function JourneyPurchaseInformation() {
  const router = useRouter();
  const t = useTranslations("shop.common.journey.purchase.information");

  const { customer } = useCustomer();

  const companyQuery = useQuery({
    queryKey: ["customer-company", customer?.company?.id],
    queryFn: () => getCompany(customer!.company?.id),
    enabled: !!customer && !!customer.company?.id,
  });

  const customerCompany = companyQuery.data;

  function handleRedirectToInformation() {
    router.push("./informations?edit=true");
  }

  const billingAddress = `${customerCompany?.billing?.street_and_number} ${customerCompany?.billing?.city}, ${customerCompany?.billing?.country_code}`;

  if (!customer) return null;

  return (
    <div className="px-4 py-6 md:p-6 bg-surface-01 rounded-3xl mt-8">
      <div className="flex justify-between items-center mb-6">
        <p className="text-primary text-2xl font-bold">{t("title")}</p>
        <button className="text-support-blue text-xl font-bold" onClick={handleRedirectToInformation}>
          {t("edit")}
        </button>
      </div>
      <div className="bg-background rounded-[20px]">
        <div className="p-4 flex flex-col gap-2">
          <p className="text-sm text-tonal-dark-cream-30">{t("fullName")}</p>
          <p className="text-base text-primary">{customerCompany?.billing?.full_name}</p>
        </div>
        <div className="p-4 flex flex-col gap-2">
          <p className="text-sm text-tonal-dark-cream-30">{t("country")}</p>
          <p className="text-base text-primary">
            {COUNTRIES.find((country) => country.code === customerCompany?.billing?.country_code)?.name}
          </p>
        </div>
        <div className="p-4 flex flex-col gap-2">
          <p className="text-sm text-tonal-dark-cream-30">{t("companyName")}</p>
          <p className="text-base text-primary">{customerCompany?.billing?.company_name}</p>
        </div>
        <div className="p-4 flex flex-col gap-2">
          <p className="text-sm text-tonal-dark-cream-30">{t("companyCountry")}</p>
          <p className="text-base text-primary">
            {COUNTRIES.find((country) => country.code === customer.company?.address.country_code)?.name}
          </p>
        </div>
        <div className="p-4 flex flex-col gap-2">
          <p className="text-sm text-tonal-dark-cream-30">{t("billingAddress")}</p>
          <p className="text-base text-primary">{billingAddress}</p>
        </div>
      </div>
    </div>
  );
}
