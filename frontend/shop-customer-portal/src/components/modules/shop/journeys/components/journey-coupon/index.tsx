"use client";

import InputVoucher from "@/components/ui/input-voucher";
import { useShoppingCart } from "@/hooks/use-shopping-cart";
import { getCoupon } from "@/lib/api/coupon";
import { formatCurrency } from "@/utils/formatCurrency";
import { Clear } from "@arthursenno/lizenzero-ui-react/Icon";
import { useSearchParams } from "next/navigation";
import { enqueueSnackbar } from "notistack";
import { ChangeEvent, useEffect, useState } from "react";
import { useTranslations } from "next-intl";
export function JourneyInformationCoupon() {
  const { shoppingCart, updateCart } = useShoppingCart();
  const t = useTranslations("shop.common.journey.shoppingCart.coupon");

  const searchParams = useSearchParams();
  const initialCoupon = searchParams.get("initial-coupon");

  const [showInputDiscountCode, setShowInputDiscountCode] = useState(false);

  const [coupon, setCoupon] = useState("");
  const [loadingCoupon, setloadingCoupon] = useState(false);
  const [errorMsgCoupon, setErrorMsgCoupon] = useState("");

  const handleChangeCoupon = (event: ChangeEvent<HTMLInputElement>) => {
    setErrorMsgCoupon("");
    setCoupon(event.target.value);
  };

  async function handleCoupon(initialCode?: string) {
    setShowInputDiscountCode(true);

    if (!coupon && !initialCode) return;

    setloadingCoupon(true);

    try {
      setCoupon("");

      await updateCart({
        coupon,
        coupon_type: `WRITTEN`,
      });

      if (initialCode && typeof initialCode === "string") {
        setCoupon(initialCode);
      }
      enqueueSnackbar("Coupon applied successfully", { variant: "success" });
    } catch {
      setErrorMsgCoupon("Invalid code");

      enqueueSnackbar("Unable to apply coupon", { variant: "error" });
    } finally {
      setloadingCoupon(false);
    }
  }

  async function handleRemoveCoupon() {
    try {
      await updateCart({ coupon: null });

      enqueueSnackbar("Coupon removed successfully", { variant: "success" });
    } catch {
      enqueueSnackbar("Unable to remove coupon", { variant: "error" });
    }
  }

  function handleClickAddCode() {
    setShowInputDiscountCode(true);
  }

  useEffect(() => {
    if (initialCoupon) {
      handleCoupon(initialCoupon);
    }
  }, []);

  return shoppingCart.coupon ? (
    <div className="flex p-4 my-7 justify-between items-center">
      <div className="flex gap-2">
        <Clear className="fill-support-blue size-5 cursor-pointer" onClick={handleRemoveCoupon} />
        <p className="text-sm text-tonal-dark-green-30">{shoppingCart.coupon.code}</p>
      </div>
      <p className="text-sm text-tonal-dark-green-30 font-bold">
        -{" "}
        {shoppingCart.coupon.discount_type === "ABSOLUTE"
          ? formatCurrency(shoppingCart.coupon.value)
          : `${shoppingCart.coupon.value} %`}
      </p>
    </div>
  ) : (
    <div className="p-4 flex justify-between items-center my-7">
      <p className="text-base text-tonal-dark-cream-30">{t("discountCode")}</p>
      {showInputDiscountCode ? (
        <InputVoucher
          placeholder={t("placeholder")}
          onChange={handleChangeCoupon}
          value={coupon}
          errorMessage={errorMsgCoupon}
          onBlur={handleCoupon}
          onClick={handleCoupon}
          loading={loadingCoupon}
        />
      ) : (
        <button onClick={handleClickAddCode} className="text-support-blue text-base">
          {t("add")}
        </button>
      )}
    </div>
  );
}
