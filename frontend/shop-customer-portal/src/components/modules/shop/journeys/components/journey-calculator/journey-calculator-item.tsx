"use client";

import { But<PERSON> } from "@arthursenno/lizenzero-ui-react/Button";
import {
  Add,
  Aluminium,
  CheckCircle,
  Delete,
  East,
  KeyboardArrowUp,
  RadioSelected,
  RadioUnselected,
  Remove,
} from "@arthursenno/lizenzero-ui-react/Icon";

import { CountryIcon } from "@/components/_common/country-icon";
import { Divider } from "@/components/_common/divider";
import { FractionInput } from "@/components/_common/forms/fraction-input";
import {
  QuestionTooltip,
  QuestionTooltipDescription,
  QuestionTooltipTitle,
} from "@/components/_common/question-tooltip";
import { Spinner } from "@/components/ui/loader";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { useCommitment } from "@/hooks/use-commitment";
import { usePricing } from "@/hooks/use-pricing";
import { useShoppingCart } from "@/hooks/use-shopping-cart";
import { SubmitCommitmentParams } from "@/lib/api/commitment";
import { ShoppingCartItem } from "@/lib/api/shoppingCart/types";
import { useTranslations } from "next-intl";
import { enqueueSnackbar } from "notistack";
import { useRef, useState } from "react";
import { Controller, useFormContext, useWatch } from "react-hook-form";
import { JourneyCalculatorFormData } from "./journey-calculator-provider";
interface JourneyCalculatorItemProps {
  cartItem: ShoppingCartItem;
}

export function JourneyCalculatorItem({ cartItem }: JourneyCalculatorItemProps) {
  const commitmentRef = useRef<HTMLDetailsElement | null>(null);
  const t = useTranslations("shop.longJourney.calculator.item");
  const globalT = useTranslations("global");

  const {
    formState: { errors, isSubmitted, isValid },
    register,
    trigger,
    clearErrors,
    control,
    getValues,
    reset,
    setValue,
    unregister,
  } = useFormContext<JourneyCalculatorFormData>();

  const { shoppingCart, isUpdatingCart, updateCartItem, deleteCartItem, updateQueryData } = useShoppingCart();
  const {
    commitment,
    isLoading: isLoadingCommitment,
    submitCommitment,
    isSubmitting: isSubmittingCommitment,
  } = useCommitment(cartItem.country_code);

  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const [isCommitmentSubmitted, setIsCommitmentSubmitted] = useState(false);
  const customerCommitment = shoppingCart.customer_commitments.find((c) => c.country_code === cartItem.country_code);
  const [isEditingCommitment, setIsEditingCommitment] = useState(!customerCommitment);

  const { priceLists, isLoading: isLoadingLicensePricing } = usePricing({
    serviceType: "EU_LICENSE",
    countryCode: cartItem.country_code,
  });

  const formItem = useWatch({
    control,
    name: `items.${cartItem.country_code}`,
  });

  if (cartItem.service_type !== "EU_LICENSE") return null;

  const status = !!customerCommitment ? "packaging" : "commitment";

  const countryCheck = !!customerCommitment && isValid;

  const unfilledQuestionErrors = errors?.items?.[cartItem.country_code]?.commitmentAnswers;

  async function handleConfirmCommitment() {
    const isValid = await trigger(`items.${cartItem.country_code}.commitmentAnswers`);

    if (!isValid) return;

    setIsCommitmentSubmitted(true);

    const commitmentAnswers = getValues(`items.${cartItem.country_code}.commitmentAnswers`);

    if (customerCommitment) {
      const isFormEqual = customerCommitment.commitment.every(
        (c) => commitmentAnswers[`criteria_${c.id}`] === c.answer
      );

      if (isFormEqual) {
        setIsEditingCommitment(false);
        return;
      }
    }

    const formattedCommitmentAnswers: SubmitCommitmentParams["commitment_answers"] = [];

    Object.entries(commitmentAnswers).forEach(([key, value]) => {
      const criteriaId = Number(key.split("_")[1]);

      if (!criteriaId) return;

      formattedCommitmentAnswers.push({
        criteria_id: criteriaId,
        answer: value,
      });
    });

    if (commitmentRef.current) commitmentRef.current.open = false;
    clearErrors(`items.${cartItem.country_code}`);

    submitCommitment(
      {
        country_code: cartItem.country_code,
        year: 2025,
        customer_email: shoppingCart.email || "",
        commitment_answers: formattedCommitmentAnswers,
        shopping_cart_id: shoppingCart.id,
      },
      {
        onSuccess: async (customerCommitment) => {
          const item = shoppingCart.items.find((item) => item.id === cartItem.id);

          if (!item) return;

          const packagingServices = customerCommitment.service_setup.packaging_services
            .filter((p) => p.obliged)
            .map((p) => ({
              id: p.id,
              name: p.name,
              fractions: p.report_set.fractions.reduce(
                (acc, fraction) => {
                  acc[fraction.code] = {
                    code: fraction.code,
                    name: fraction.name,
                    weight: 0,
                  };
                  return acc;
                },
                {} as Record<string, { code: string; name: string; weight: number }>
              ),
            }));

          const updatedItem: ShoppingCartItem = {
            ...item,
            packaging_services: packagingServices,
          };

          setValue(
            `items.${cartItem.country_code}.packagingServices`,
            packagingServices.reduce(
              (acc, p) => {
                acc[`p_${p.id}`] = p;
                return acc;
              },
              {} as Record<string, any>
            )
          );

          updateQueryData({
            ...shoppingCart,
            items: shoppingCart.items.map((item) => (item.id === cartItem.id ? updatedItem : item)),
          });
          setIsEditingCommitment(false);

          enqueueSnackbar(t("commitment.successful"), { variant: "success" });
          updateCartItem(item.id, updatedItem);
        },
        onError: () => {
          enqueueSnackbar(t("commitment.error"), { variant: "error" });
        },
      }
    );
  }

  function handleEditCommitment() {
    setIsEditingCommitment(true);
  }

  const isCommitmentFilled = !!customerCommitment;

  async function handleRemoveItem() {
    await deleteCartItem(cartItem.id);
    unregister(`items.${cartItem.country_code}`);
  }

  function handleAddPackagingService(packagingServiceId: number) {
    if (!customerCommitment) return;

    const packagingService = customerCommitment.service_setup.packaging_services.find(
      (p) => p.id === packagingServiceId
    );

    if (!packagingService) return;

    setValue(`items.${cartItem.country_code}.packagingServices.p_${packagingServiceId}`, {
      id: packagingService.id,
      name: packagingService.name,
      fractions: packagingService.report_set.fractions.reduce(
        (acc, fraction) => {
          acc[fraction.code] = {
            code: fraction.code,
            name: fraction.name,
            weight: 0,
          };
          return acc;
        },
        {} as Record<string, { code: string; name: string; weight: number }>
      ),
    });
  }

  function handleRemovePackagingService(packagingServiceId: number) {
    const currentPackagingServices = getValues(`items.${cartItem.country_code}.packagingServices`);

    delete currentPackagingServices[`p_${packagingServiceId}`];

    setValue(`items.${cartItem.country_code}.packagingServices`, currentPackagingServices);
  }

  async function handleChangeLicenseYear(year: number) {
    await updateCartItem(cartItem.id, { year });
  }

  async function handleUpdateFractionWeights() {
    if (debounceTimeoutRef.current) clearTimeout(debounceTimeoutRef.current);

    debounceTimeoutRef.current = setTimeout(async () => {
      const formPackagingServices = { ...getValues(`items.${cartItem.country_code}.packagingServices`) };

      if (!formPackagingServices) return;

      const data: ShoppingCartItem = {
        ...cartItem,
        packaging_services: Object.values(formPackagingServices || {}) as ShoppingCartItem["packaging_services"],
      };

      await updateCartItem(cartItem.id, data);
    }, 500);
  }

  return (
    <details className="group rounded-4xl overflow-hidden" open={true}>
      <summary
        data-status={status}
        className="flex items-center justify-between px-4 py-6 md:py-7 md:px-10 bg-surface-04 data-[status=commitment]:bg-tonal-dark-blue-90"
      >
        <div className="flex items-center gap-4">
          <CountryIcon
            country={{ flag_url: cartItem.country_flag, name: cartItem.country_name }}
            className="size-6 md:size-8"
          />
          <p className="text-2xl font-bold text-primary">{cartItem.country_name}</p>
          <Button color="light-blue" variant="text" size="small" onClick={handleRemoveItem} type="button">
            {t("remove")}
          </Button>
        </div>
        <div className="flex items-center gap-2">
          {countryCheck && <CheckCircle className="size-6 flex-none fill-success" />}
          <KeyboardArrowUp className="flex-none size-8 fill-primary group-open:-rotate-180 transition-all duration-300" />
        </div>
      </summary>
      <div className="bg-surface-02 px-6 md:px-10">
        <div className="flex items-center justify-between pt-4 gap-2">
          <p className="text-tonal-dark-cream-30 text-sm">{t("preInformation")}</p>
          {priceLists && (
            <div className="flex items-center gap-2">
              <QuestionTooltip>
                <QuestionTooltipDescription className="p-1">{t("preInformationTooltip")}</QuestionTooltipDescription>
              </QuestionTooltip>
              <Select
                defaultValue={cartItem.year?.toString()}
                onValueChange={(value: string) => handleChangeLicenseYear(Number(value))}
              >
                <SelectTrigger className="bg-transparent border-none w-auto p-1 rounded-lg text-support-blue font-bold focus:ring-support-blue">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent className="z-[1002]">
                  {priceLists?.map((priceList) => (
                    <SelectItem key={priceList.id} value={priceList.condition_type_value}>
                      {priceList.condition_type_value}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
          {!priceLists && <Skeleton className="h-6 w-24" />}
        </div>
        <details ref={commitmentRef} className="group/commitment" open={!isCommitmentFilled}>
          <summary className="flex flex-col">
            <div className="flex items-center justify-between py-6">
              <div className="flex items-center gap-4">
                <p className="text-xl font-bold text-primary">{t("commitmentAssessment.title")}</p>
                <QuestionTooltip>
                  <QuestionTooltipDescription>{t("commitmentAssessment.description")}</QuestionTooltipDescription>
                </QuestionTooltip>
              </div>
              <Remove className="hidden group-open/commitment:block size-8 fill-support-blue cursor-pointer" />
              <Add className="block group-open/commitment:hidden size-8 fill-support-blue cursor-pointer" />
            </div>
            {(isCommitmentFilled || isSubmittingCommitment) && (
              <Divider className="block group-open/commitment:hidden m-0" style={{ margin: 0 }} />
            )}
          </summary>
          <div className="flex flex-col pb-6">
            <div className="space-y-6 md:space-y-10">
              {!!commitment?.length &&
                commitment.map((question) => (
                  <div key={`${cartItem.country_code}-question-${question.id}`} className="flex flex-col gap-4">
                    <p className="text-base text-tonal-dark-cream-10">{question.title}</p>
                    {question.options.map((option) => (
                      <label
                        key={`${cartItem.country_code}-question-${question.id}-option-${option.id}`}
                        className="text-base text-tonal-dark-cream-20 flex gap-2 items-center cursor-pointer has-[input:checked]:cursor-default"
                      >
                        <input
                          type="radio"
                          {...register(`items.${cartItem.country_code}.commitmentAnswers.${`criteria_${question.id}`}`)}
                          value={option.value}
                          disabled={!isEditingCommitment || isSubmittingCommitment}
                          className="hidden peer"
                          data-invalid={!!unfilledQuestionErrors?.[`criteria_${question.id}`]}
                        />
                        <RadioSelected className="hidden peer-checked:block size-5 fill-primary" />
                        <RadioUnselected className="block cursor-pointer peer-checked:hidden size-5 fill-primary peer-data-[invalid=true]:fill-error" />
                        {question.input_type === "SELECT" && <span className="mt-1">{option.option_value}</span>}
                        {question.input_type === "YES_NO" && (
                          <>
                            {option.option_value === "YES"
                              ? globalT("inputs.yesOrNo.yes")
                              : globalT("inputs.yesOrNo.no")}
                          </>
                        )}
                      </label>
                    ))}
                    {!!unfilledQuestionErrors?.[`criteria_${question.id}`] && (
                      <div className="flex items-center gap-2">
                        <p className="text-sm text-error">Missing information</p>
                        <QuestionTooltip className="fill-error hover:fill-error-opacity">
                          <QuestionTooltipDescription>
                            Please select an option for this question.
                          </QuestionTooltipDescription>
                        </QuestionTooltip>
                      </div>
                    )}
                  </div>
                ))}
              {isLoadingCommitment && (
                <>
                  <div className="flex flex-col gap-4">
                    <Skeleton className="h-6 w-full" />
                    <div className="space-y-4">
                      <div className="flex items-center gap-2">
                        <Skeleton className="h-5 w-5 rounded-full" />
                        <Skeleton className="h-4 w-1/2" />
                      </div>
                      <div className="flex items-center gap-2">
                        <Skeleton className="h-5 w-5 rounded-full" />
                        <Skeleton className="h-4 w-1/2" />
                      </div>
                      <div className="flex items-center gap-2">
                        <Skeleton className="h-5 w-5 rounded-full" />
                        <Skeleton className="h-4 w-1/2" />
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-col gap-4">
                    <Skeleton className="h-6 w-full" />
                    <div className="space-y-4">
                      <div className="flex items-center gap-2">
                        <Skeleton className="h-5 w-5 rounded-full" />
                        <Skeleton className="h-4 w-1/2" />
                      </div>
                      <div className="flex items-center gap-2">
                        <Skeleton className="h-5 w-5 rounded-full" />
                        <Skeleton className="h-4 w-1/2" />
                      </div>
                      <div className="flex items-center gap-2">
                        <Skeleton className="h-5 w-5 rounded-full" />
                        <Skeleton className="h-4 w-1/2" />
                      </div>
                    </div>
                  </div>
                </>
              )}
              <Divider style={{ marginTop: 24 }} />
            </div>
            {!isLoadingCommitment && (
              <>
                {!isEditingCommitment ? (
                  <div className="flex flex-col justify-center gap-4 pt-6">
                    <div>
                      <Button
                        type="button"
                        color="dark-blue"
                        variant="filled"
                        size="small"
                        style={{ padding: "10px 44px" }}
                        onClick={handleEditCommitment}
                      >
                        {t("commitmentAssessment.editAnswers")}
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="flex flex-col items-end justify-center gap-4 pt-6">
                    {!!unfilledQuestionErrors && (
                      <p className="text-error">{t("commitmentAssessment.answerAllQuestions")}</p>
                    )}
                    <Button
                      color={!!unfilledQuestionErrors ? "red" : "yellow"}
                      disabled={!!unfilledQuestionErrors || isSubmittingCommitment}
                      variant="filled"
                      size="medium"
                      onClick={handleConfirmCommitment}
                      trailingIcon={<East />}
                    >
                      {t("commitmentAssessment.confirm")}
                    </Button>
                  </div>
                )}
              </>
            )}
          </div>
        </details>
        {!isSubmittingCommitment && isCommitmentFilled && !!customerCommitment && (
          <div className="flex flex-col gap-8 py-5 md:py-10">
            {!!customerCommitment?.service_setup?.packaging_services?.length &&
              customerCommitment?.service_setup?.packaging_services?.map((packagingService) => {
                const packagingErrors =
                  errors?.items?.[cartItem.country_code]?.packagingServices?.[packagingService.id];

                const fractionErrors = packagingErrors?.fractions;

                return (
                  <div
                    key={`${cartItem.country_code}_${packagingService.id}`}
                    className="p-4 md:p-8 rounded-4xl bg-surface-03"
                  >
                    <div className="flex items-center gap-4">
                      <p className="text-xl font-bold text-tonal-dark-cream-30 mt-1">{t("packagingService.result")}:</p>
                      <p
                        data-license={packagingService.obliged}
                        className="text-base font-bold mt-1 text-tonal-dark-green-30 data-[license=true]:text-on-surface-04"
                      >
                        {packagingService.obliged
                          ? t("packagingService.licensingRequired")
                          : t("packagingService.licensingNotRequired")}
                      </p>
                      {formItem?.packagingServices?.[`p_${packagingService.id}`] && (
                        <QuestionTooltip>
                          <QuestionTooltipDescription>
                            {t("packagingService.packagingVolumeTooltip")}
                          </QuestionTooltipDescription>
                        </QuestionTooltip>
                      )}
                    </div>
                    {!formItem?.packagingServices?.[`p_${packagingService.id}`] && (
                      <p className="text-sm text-tonal-dark-cream-30 mt-4 mb-6">
                        {t("packagingService.packagingVolumeDescription")}
                      </p>
                    )}
                    <Divider style={{ margin: "20px 0" }} />
                    {formItem?.packagingServices?.[`p_${packagingService.id}`] ? (
                      <div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4">
                            <p
                              data-invalid={!!packagingErrors}
                              className="text-2xl font-bold text-primary data-[invalid=true]:text-error"
                            >
                              {packagingService.name}
                            </p>
                            <QuestionTooltip>
                              <QuestionTooltipDescription>
                                {t("packagingService.serviceTooltip")}
                              </QuestionTooltipDescription>
                            </QuestionTooltip>
                          </div>
                          <Button
                            variant="text"
                            color="gray"
                            size="iconXSmall"
                            onClick={() => handleRemovePackagingService(packagingService.id)}
                          >
                            <Delete className="size-5 fill-tonal-dark-cream-40" />
                          </Button>
                        </div>
                        <div>
                          <p
                            data-invalid={!!packagingErrors && isSubmitted}
                            className="text-sm text-tonal-dark-cream-30 data-[invalid=true]:text-error my-5"
                          >
                            {t("packagingService.estimateQuantity")}
                          </p>
                          <div className="rounded-[20px] overflow-hidden space-y-[1px] bg-tonal-dark-cream-80">
                            {Object.values(packagingService.report_set.fractions).map((fraction) => (
                              <div
                                className="bg-white"
                                key={`${cartItem.country_code}-fractionContainer-${fraction.name}`}
                              >
                                <div className="py-[14px] px-5 flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
                                  <div className="flex items-center gap-4">
                                    <QuestionTooltip>
                                      <QuestionTooltipTitle>
                                        <Aluminium width={24} className="fill-primary" />
                                        <p className="text-primary text-md font-bold">{fraction.name}</p>
                                      </QuestionTooltipTitle>
                                      <QuestionTooltipDescription className="text-primary">
                                        {fraction.description}
                                      </QuestionTooltipDescription>
                                    </QuestionTooltip>

                                    <div className="flex flex-1 items-center gap-3">
                                      <Aluminium className="size-6 md:size-9 fill-primary" />
                                      <p className="text-sm md:text-base font-bold text-primary">{fraction.name}</p>
                                    </div>
                                  </div>
                                  <div className="flex items-center gap-4 w-full md:w-48 flex-shrink-0">
                                    <Controller
                                      key={`${cartItem.country_code}-fraction-${fraction.code}`}
                                      name={`items.${cartItem.country_code}.packagingServices.p_${packagingService.id}.fractions.${fraction.code}.weight`}
                                      control={control}
                                      render={({ field }) => (
                                        <FractionInput
                                          {...field}
                                          type="weight"
                                          data-invalid={!!fractionErrors && !!fractionErrors[fraction.code]}
                                          onChange={(value) => field.onChange(value)}
                                          className="fraction-input"
                                          onBlur={() => {
                                            setTimeout(() => {
                                              if (!document.activeElement?.classList.contains("fraction-input")) {
                                                handleUpdateFractionWeights();
                                              }
                                            }, 0);
                                          }}
                                        />
                                      )}
                                    />
                                    <span className="text-base text-primary">kg</span>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div>
                        <div className="flex items-center justify-between">
                          <p className="text-2xl font-bold text-primary">{packagingService.name}</p>
                          <div className="flex items-center gap-10">
                            <Button
                              variant="filled"
                              color="yellow"
                              size="iconXSmall"
                              onClick={() => handleAddPackagingService(packagingService.id)}
                            >
                              <Add className="size-5 fill-primary" />
                            </Button>
                          </div>
                        </div>
                        <p className="mt-2 text-sm text-tonal-dark-cream-30">
                          {t("packagingService.addPackagingServiceDescription")}
                        </p>
                      </div>
                    )}
                  </div>
                );
              })}
            {!customerCommitment?.service_setup?.packaging_services?.length && (
              <div className="flex justify-center items-center gap-1 md:gap-2 px-2 py-3 border-[1px] border-tonal-dark-cream-80 rounded-md">
                <Spinner size="sm" />
                <p className="text-center text-primary">{t("packagingService.loading")}</p>
              </div>
            )}
          </div>
        )}
        {isCommitmentSubmitted && (isSubmittingCommitment || !customerCommitment) && (
          <div className="space-y-8 py-5 md:py-10">
            <Skeleton className="h-24 w-full" />
            <Skeleton className="h-24 w-full" />
          </div>
        )}
      </div>
    </details>
  );
}
