import { PaymentMethodType } from "@/lib/api/payment/types";
import { PAYMENT_METHODS } from "./payment-methods";
import { PaymentMethod } from "./payment-method";

interface JourneyBillingPaymentMethodsProps {
  borderLess?: boolean;
  paymentMethod?: PaymentMethodType;
  handleSetPaymentMethod?: (type: PaymentMethodType | string) => void;
}

export function JourneyBillingPaymentMethods({
  borderLess,
  paymentMethod,
  handleSetPaymentMethod,
}: JourneyBillingPaymentMethodsProps) {
  return (
    <div className="flex flex-col w-full">
      {PAYMENT_METHODS.map((method, index) => (
        <PaymentMethod
          key={index}
          {...method}
          borderLess={borderLess}
          paymentMethod={paymentMethod}
          handleSetPaymentMethod={handleSetPaymentMethod}
        />
      ))}
    </div>
  );
}
