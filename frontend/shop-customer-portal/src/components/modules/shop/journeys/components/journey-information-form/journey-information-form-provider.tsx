"use client";

import {
  additionalAddressLine,
  city,
  companyId,
  companyName,
  countryCode,
  documentType,
  emails,
  firstName,
  lucidNumber,
  mobile,
  phone,
  phones,
  salutation,
  streetAndNumber,
  surname,
  taxNumber,
  vatId,
  zipCode,
} from "@/components/_common/forms/schemas";
import { useCustomer } from "@/hooks/use-customer";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { ReactNode, useEffect } from "react";
import { FormProvider, useForm } from "react-hook-form";

import { z } from "zod";

const journeyInformationFormSchema = z.object({
  companyId,
  companyName,
  countryCode,
  city,
  zipCode,
  streetAndNumber,
  additionalAddressLine,
  documentType,
  vatId,
  taxNumber,
  salutation,
  firstName,
  surname,
  phone,
  mobile,
  emails,
  lucidNumber,
  phones,
});

export type JourneyInformationFormData = z.infer<typeof journeyInformationFormSchema>;

interface JourneyInformationFormProviderProps {
  children: ReactNode;
}

export function JourneyInformationFormProvider({ children }: JourneyInformationFormProviderProps) {
  const { customer } = useCustomer();
  const t = useTranslations("shop.common.journey.information");

  const methods = useForm<JourneyInformationFormData>({
    resolver: zodResolver(journeyInformationFormSchema),
    mode: "all",
  });

  useEffect(() => {
    if (!customer) return;

    const customerCompany = customer.company;

    (async () => {
      methods.reset({
        firstName: customer.first_name,
        surname: customer.last_name,
        companyId: customer.company?.id || undefined,
        companyName: customerCompany?.name || customer.company_name || undefined,
        countryCode: customerCompany?.address.country_code,
        city: customerCompany?.address.city,
        zipCode: customerCompany?.address.zip_code,
        streetAndNumber: customerCompany?.address.street_and_number,
        additionalAddressLine: customerCompany?.address.additional_address,
        documentType: customerCompany?.vat ? "VAT" : "TAX",
        vatId: customerCompany?.vat || undefined,
        taxNumber: customerCompany?.tin || undefined,
        lucidNumber: customerCompany?.lucid || undefined,
        salutation: customer.salutation || t("personal.salutation.masculine"),
        phones: customer.phones?.slice(2, customer.phones?.length).map((phone) => ({
          id: String(phone.id),
          phone_number: phone.phone_number,
        })),
        phone: customer.phones?.[0]?.phone_number,
        mobile: customer.phones?.[1]?.phone_number,
        emails: customerCompany?.emails || [],
      });
    })();
  }, [customer, customer?.company?.id]);

  return <FormProvider {...methods}>{children}</FormProvider>;
}
