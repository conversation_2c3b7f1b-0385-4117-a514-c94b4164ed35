"use client";

import { CountryInput } from "@/components/_common/forms/country-input";
import { Icons } from "@/components/ui/icons";
import { useLiberatedCountries } from "@/hooks/use-liberated-countries";
import { useShoppingCart } from "@/hooks/use-shopping-cart";
import { useCustomer } from "@/hooks/use-customer";
import { enqueueSnackbar } from "notistack";
import { useFormContext } from "react-hook-form";

import { JourneySelectCountriesFormData } from "./journey-select-countries-provider";
import { Skeleton } from "@/components/ui/skeleton";
import { useTranslations } from "next-intl";
import { JourneySelectCountriesItem } from "./journey-select-countries-item";
import { BaseCountry } from "@/lib/api/country/types";

export function JourneySelectCountries() {
  const t = useTranslations("shop.longJourney.selectCountries");
  const { shoppingCart, addCartItem, isUpdatingCart } = useShoppingCart();
  const { customer } = useCustomer();

  const { liberatedCountries } = useLiberatedCountries();

  async function handleSelectCountry(country: BaseCountry) {
    const publishedCountry = liberatedCountries.find((c) => c.code === country.code);

    if (!publishedCountry) return;

    try {
      await addCartItem(country.code, "EU_LICENSE");

      enqueueSnackbar(t("select.success"), { variant: "success" });
    } catch {
      enqueueSnackbar(t("select.error"), { variant: "error" });
    }
  }

  const licenseItems = shoppingCart.items.filter((i) => i.service_type === "EU_LICENSE");

  const {
    formState: { errors },
  } = useFormContext<JourneySelectCountriesFormData>();

  const customerCommitments = shoppingCart.customer_commitments;
  const allFilled =
    !!licenseItems.length &&
    licenseItems.every((i) => !!customerCommitments.find((c) => c.country_code === i.country_code));
  const isValid = !errors.items;

  const contractedLicenses = (() => {
    if (!customer) return [];

    const euLicenseContract = customer.contracts.find((c) => c.type === "EU_LICENSE");

    if (!euLicenseContract) return [];

    return euLicenseContract.licenses;
  })();

  const searchCountries = liberatedCountries.filter(
    (c) =>
      !licenseItems.find((item) => item.country_code === c.code) &&
      !contractedLicenses.find((l) => l.country_code === c.code)
  );

  return (
    <form
      key={shoppingCart.updated_at}
      className="bg-surface-02 py-6 md:py-10 px-4 md:px-6 rounded-[32px] flex flex-col gap-6"
    >
      <div className="hidden md:block">
        <p className="text-grey-blue font-bold text-2xl">{t("title")}</p>
        <span className="text-grey-blue text-sm">{t("description")}</span>
      </div>
      <div className="w-full md:w-2/3">
        <CountryInput
          countries={searchCountries}
          onSelectCountry={handleSelectCountry}
          recommendationEnabled
          contractedCountryCodes={contractedLicenses.map((l) => l.country_code)}
        />
      </div>
      {!!licenseItems.length && (
        <p className="text-tonal-dark-cream-30 font-bold text-sm">{t("questions.description")}</p>
      )}
      {!isUpdatingCart && (
        <>
          {allFilled && isValid && (
            <div className="p-4 flex items-center rounded-2xl bg-[#F0FAF0] gap-6">
              <div>
                <Icons.badge className="w-12 md:w-8 h-12 fill-tonal-dark-green-30" />
              </div>
              <p className="font-bold text-tonal-dark-green-30">{t("questions.success")}</p>
            </div>
          )}
          {!!licenseItems.length && (
            <div className="space-y-6">
              {licenseItems.map((item) => (
                <JourneySelectCountriesItem key={item.id} cartItem={item} />
              ))}
            </div>
          )}
        </>
      )}
      {isUpdatingCart && (
        <div className="space-y-6">
          {Array.from({ length: licenseItems.length || 1 }).map((_, index) => (
            <div key={index} className="group rounded-4xl bg-background flex items-center gap-3  py-5 px-4 md:px-7">
              <Skeleton className="flex-none size-8 rounded-full" />
              <Skeleton className="h-8 w-full" />
            </div>
          ))}
        </div>
      )}
    </form>
  );
}
