"use client";

import { SaveProgress } from "@/components/ui/btn-save-my-progress";
import { useShoppingCart } from "@/hooks/use-shopping-cart";
import { useRouter } from "@/i18n/navigation";
import { ShoppingCartItem } from "@/lib/api/shoppingCart/types";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { East } from "@arthursenno/lizenzero-ui-react/Icon";
import { useSession } from "next-auth/react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { useFormContext, useWatch } from "react-hook-form";
import { JourneyCalculatorFormData } from "./journey-calculator-provider";

export function JourneyCalculatorSubmit() {
  const router = useRouter();
  const t = useTranslations("shop.quickJourney.license.calculator.submit");

  const session = useSession();
  const [isLoading, setIsLoading] = useState(false);
  const { shoppingCart, updateCartItems } = useShoppingCart();
  const {
    handleSubmit,
    formState: { errors, isSubmitted },
    control,
  } = useFormContext<JourneyCalculatorFormData>();

  const formItems = useWatch({ control, name: "items" });

  const errorMessage = (() => {
    const licenseItems = shoppingCart.items.filter((i) => i.service_type === "EU_LICENSE");

    if (!licenseItems.length) return t("errors.noLicenseItems");

    const countryWithoutPack = Object.values(formItems || {}).some(
      (i) => !Object.values(i.packagingServices || {}).length
    );

    if (countryWithoutPack) return t("errors.noPackagingServices");

    for (const [countryCode, item] of Object.entries(formItems || {})) {
      const hasAddedPackagingServices = Object.values(item.packagingServices || {}).length;

      const customerCommitment = shoppingCart.customer_commitments.find((c) => c.country_code === countryCode);

      if (hasAddedPackagingServices && !customerCommitment) {
        return t("errors.formNotFilled");
      }
    }

    if (!errors || Object.keys(errors).length === 0) return null;

    if (errors.items?.minimum?.message) return errors.items?.minimum?.message;

    return t("errors.formNotFilled");
  })();

  async function handleSubmitCalculator(data: JourneyCalculatorFormData) {
    try {
      if (errorMessage) return;
      setIsLoading(true);

      await updateCartItems(
        shoppingCart.items.map((item) => ({
          ...item,
          packaging_services: Object.values(
            data.items[item.country_code]?.packagingServices || {}
          ) as ShoppingCartItem["packaging_services"],
        }))
      );

      if (shoppingCart.journey !== "LONG") {
        if (!session.data?.user) return router.push("./create-account");

        if (!session.data.user.has_password) return router.push("./set-password");

        return router.push("./shopping-cart");
      }

      router.push("./shopping-cart");
    } catch (err) {
      console.log(err);
      setIsLoading(false);
    }
  }

  const isError = isSubmitted && !!errorMessage;

  return (
    <div className="space-y-4">
      {isError && <p className="text-right text-error">{errorMessage}</p>}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-12">
        <SaveProgress />
        <Button
          trailingIcon={<East />}
          color={isError ? "red" : "yellow"}
          variant="filled"
          size="medium"
          onClick={() => handleSubmit(handleSubmitCalculator)()}
          disabled={isLoading}
          type="button"
        >
          {isLoading ? t("loading") : t("label")}
        </Button>
      </div>
    </div>
  );
}
