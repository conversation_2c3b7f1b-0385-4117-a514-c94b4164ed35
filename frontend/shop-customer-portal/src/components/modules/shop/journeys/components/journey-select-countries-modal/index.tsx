"use client";

import { CountryRecommendationModal } from "@/components/_common/modals/country-recommendation-modal";
import { CountryRecommendationModalTrigger } from "@/components/_common/modals/country-recommendation-modal/country-recommendation-modal-trigger";
import { CountryInput } from "@/components/_common/forms/country-input";
import { MapCountries } from "@/components/_common/map";
import { TitleAndSubTitle } from "@/components/_common/titleAndSubTitle";
import { useJourney } from "@/hooks/use-journey";
import { useLiberatedCountries } from "@/hooks/use-liberated-countries";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { useShoppingCart } from "@/hooks/use-shopping-cart";
import { usePathname } from "@/i18n/navigation";
import { ContractType } from "@/lib/api/contracts/types";
import { BaseCountry } from "@/lib/api/country/types";
import { useCustomer } from "@/hooks/use-customer";
import { MapBanner } from "@arthursenno/lizenzero-ui-react/Banner";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { East, Error } from "@arthursenno/lizenzero-ui-react/Icon";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import { zodResolver } from "@hookform/resolvers/zod";
import { useSession } from "next-auth/react";
import { useTranslations } from "next-intl";
import { useEffect } from "react";
import { useForm, useWatch } from "react-hook-form";

import { z } from "zod";
import { JourneySelectCountriesModalItem } from "./journey-select-countries-modal-item";

const selectCountriesFormSchema = z.object({
  countries: z
    .array(
      z.object({
        id: z.number(),
        code: z.string(),
        name: z.string(),
        flag_url: z.string(),
      })
    )
    .min(1, "Please select at least one country"),
});

type SelectCountriesFormData = z.infer<typeof selectCountriesFormSchema>;

export function JourneySelectCountriesModal() {
  const session = useSession();
  const path = usePathname();
  const t = useTranslations("shop.common.selectCountriesModal");

  const journey = useJourney();
  const { customer } = useCustomer();
  const { liberatedCountries } = useLiberatedCountries();
  const { shoppingCart, updateCartItems, isUpdatingCart } = useShoppingCart();

  const { paramValues, deleteParam } = useQueryFilter(["select-countries", "initial-selected-countries"]);

  const isOpen = paramValues["select-countries"] === "true";
  const countriesParam = paramValues["initial-selected-countries"];

  const {
    formState: { errors },
    control,
    setValue,
    handleSubmit,
    clearErrors,
    trigger,
  } = useForm<SelectCountriesFormData>({
    resolver: zodResolver(selectCountriesFormSchema),
    defaultValues: {
      countries: [],
    },
  });

  const selectedCountries = useWatch({
    control,
    name: "countries",
  });

  const contractedCountryCodes = (() => {
    if (!customer) return [];
    if (!shoppingCart) return [];

    if (shoppingCart.journey !== "QUICK_ACTION_GUIDE" && shoppingCart.journey !== "QUICK_LICENSE") return [];

    if (shoppingCart.journey === "QUICK_ACTION_GUIDE") {
      const actionGuideContract = customer.contracts.find((c) => c.type === "ACTION_GUIDE");

      if (!actionGuideContract) return [];

      return actionGuideContract.action_guides.map((c) => c.country_code);
    }

    const euLicenseContract = customer.contracts.find((c) => c.type === "EU_LICENSE");

    if (!euLicenseContract) return [];

    return euLicenseContract.licenses.map((l) => l.country_code);
  })();

  function handleCreateAccount() {
    journey.redirectToStep("CREATE_ACCOUNT");
  }

  async function handleContinue({ countries }: SelectCountriesFormData) {
    if (isUpdatingCart) return;

    if (!countries.length) return;

    const journeyType = journey.currentJourney?.type;

    if (!journeyType) return [];

    const serviceType = journeyType === "QUICK_ACTION_GUIDE" ? "ACTION_GUIDE" : "EU_LICENSE";

    const otherTypeItems = shoppingCart.items.filter((item) => item.service_type !== serviceType);
    const currentTypeItems = shoppingCart.items.filter((item) => item.service_type === serviceType);

    const newTypeItems = countries.map((c) => {
      const isAlreadyInCart = currentTypeItems.find((item) => item.country_code === c.code);

      if (isAlreadyInCart) return isAlreadyInCart;

      return {
        country_id: c.id,
        country_code: c.code,
        country_name: c.name,
        country_flag: c.flag_url,
        year: new Date().getFullYear(),
        service_type: serviceType as ContractType,
      };
    });

    if (!newTypeItems.length) return;

    if (newTypeItems.length === currentTypeItems.length) {
      const currentTypeCountryCodes = currentTypeItems.map((item) => item.country_code);
      const newTypeCountryCodes = newTypeItems.map((item) => item.country_code);

      if (currentTypeCountryCodes.every((code) => newTypeCountryCodes.includes(code))) {
        handleOpenChange(false);
        return;
      }
    }

    updateCartItems([...otherTypeItems, ...newTypeItems]);

    handleOpenChange(false);

    if (shoppingCart.journey === "QUICK_ACTION_GUIDE") {
      if (journey.currentJourneyStep?.key !== "SHOPPING_CART") {
        journey.redirectToStep("SHOPPING_CART");
        return;
      }

      return;
    }

    if (!path.includes("/calculator")) {
      journey.redirectToStep("CALCULATOR");
    }
  }

  function handleOpenChange(open: boolean) {
    if (!open) {
      deleteParam("select-countries");

      const newCountries = getModalCountries();

      setValue("countries", newCountries);
      if (newCountries.length) {
        clearErrors("countries");
      }
    }
  }

  function handleSelectCountry(code: string) {
    const alreadySelected = selectedCountries.find((c) => c.code === code);
    const alreadyHaveLicense = contractedCountryCodes.find((c) => c === code);

    if (alreadyHaveLicense) return;

    if (!alreadySelected) {
      const country = liberatedCountries.find((country) => country.code === code);

      if (!country) return;

      setValue("countries", [...selectedCountries, country]);
      trigger("countries");

      return;
    }

    const country = liberatedCountries.find((country) => country.code === code);

    if (!country) return;

    handleRemoveCountry(country.code);
  }

  function handleAddCountry(country: BaseCountry) {
    handleSelectCountry(country.code);
  }

  function handleRemoveCountry(countryCode: string) {
    setValue(
      "countries",
      selectedCountries.filter((c) => c.code !== countryCode)
    );
    trigger("countries");
  }

  function getModalCountries() {
    if (!shoppingCart) return [];

    const journeyType = journey.currentJourney?.type;

    if (!journeyType) return [];

    const serviceType = journeyType === "QUICK_ACTION_GUIDE" ? "ACTION_GUIDE" : "EU_LICENSE";

    const countriesItems = shoppingCart.items.filter(({ service_type }) => service_type === serviceType);

    if (!countriesParam) {
      return countriesItems.map((item) => ({
        id: item.country_id,
        code: item.country_code,
        name: item.country_name,
        flag_url: item.country_flag,
      }));
    }

    const urlCountries = countriesParam.toLocaleLowerCase().split(",");

    if (!Array.isArray(urlCountries)) {
      return countriesItems.map((item) => ({
        id: item.country_id,
        code: item.country_code,
        name: item.country_name,
        flag_url: item.country_flag,
      }));
    }

    const filteredUrlCountries = urlCountries.reduce((total, country) => {
      const liberatedCountryExists = liberatedCountries.find((c) => c.code.toLocaleLowerCase() === country);

      if (!liberatedCountryExists) return total;

      return [...total, liberatedCountryExists];
    }, [] as BaseCountry[]);

    return filteredUrlCountries;
  }

  useEffect(() => {
    const newCountries = getModalCountries();
    setValue("countries", newCountries);

    if (newCountries.length) clearErrors("countries");
  }, []);

  return (
    <Modal
      open={isOpen}
      onOpenChange={handleOpenChange}
      style={{
        overflowY: "auto",
        overflowX: "hidden",
        padding: 0,
        width: "100%",
        maxWidth: "100vw",
        height: "100vh",
        zIndex: 50,
      }}
    >
      <div className="w-full h-full max-w-7xl mx-auto pt-16 pb-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-9 h-full overflow-auto px-4">
          <div className="md:h-full flex flex-col justify-start md:overflow-hidden">
            <TitleAndSubTitle
              showIcon
              title={t("title")}
              subTitle={t("subTitle")}
              subText={t("subText")}
              tooltipInfo={t("tooltipInfo")}
              className="md:pb-8"
            />
            <div className="w-full overflow-auto rounded-[32px] items-start bg-tonal-cream-96 flex flex-col px-8 py-5 gap-6">
              <p className="text-primary text-sm">{t("assessAdditionalCountries")}</p>
              <div className="w-3/5 relative">
                <CountryInput
                  countries={liberatedCountries}
                  onSelectCountry={handleAddCountry}
                  contractedCountryCodes={contractedCountryCodes}
                  recommendationEnabled
                />
              </div>
              {!!selectedCountries.length && (
                <>
                  <p className="text-tonal-dark-cream-30 text-sm">
                    {selectedCountries.length}/{liberatedCountries.length}{" "}
                    {t("countriesSelected", { count: selectedCountries.length })}
                  </p>

                  <div className="overflow-auto flex flex-col gap-6 w-full pr-2 pb-3 max-h-[264px] min-h-20">
                    {selectedCountries.map((country) => (
                      <JourneySelectCountriesModalItem
                        name={country.name || ""}
                        tooltipInfo={t("selectCountries", { name: country.name })}
                        img={country.flag_url || ""}
                        onDel={() => handleRemoveCountry(country.code || "")}
                        key={`${country.code}-journey-select-item`}
                      />
                    ))}
                  </div>
                </>
              )}
            </div>
          </div>
          <div className="md:h-full md:overflow-auto flex flex-col gap-8">
            <div className="flex-grow flex min-h-[300px]">
              <MapCountries
                key={shoppingCart.updated_at}
                onSelectCountry={handleSelectCountry}
                selectedCountries={selectedCountries.map((country) => country.code)}
                liberatedCountries={liberatedCountries}
                contractedCountryCodes={contractedCountryCodes}
              >
                <div className="absolute top-0 mt-3 ml-3">
                  <MapBanner title={t("benefitCommunication")} style={{ width: "100%", zIndex: 1000 }}>
                    <Error
                      width={"6%"}
                      height={"6%"}
                      className="fill-primary mr-2.5 min-w-6 min-h-6 max-w-[50px] max-h-[50px]"
                    />
                    <p>
                      {t("cantFindCountry")}{" "}
                      <CountryRecommendationModalTrigger className="text-primary" recommendation="" />
                    </p>
                  </MapBanner>
                </div>
              </MapCountries>
            </div>

            <div className="space-y-2">
              {!!Object.keys(errors).length && <p className="text-error text-right block">{t("selectOneCountry")}</p>}
              <div className="w-full flex justify-end gap-6">
                {session.status === "unauthenticated" && (
                  <Button
                    color="dark-blue"
                    variant="outlined"
                    size="medium"
                    onClick={handleCreateAccount}
                    style={{ width: "40%", textWrap: "nowrap" }}
                  >
                    {t("createAccount")}
                  </Button>
                )}
                <Button
                  trailingIcon={<East />}
                  color={!!Object.keys(errors).length ? "red" : "yellow"}
                  variant="filled"
                  size="medium"
                  onClick={() => handleSubmit(handleContinue)()}
                  disabled={!!Object.keys(errors).length || isUpdatingCart}
                  style={{ width: "40%" }}
                >
                  {t("continue")}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <CountryRecommendationModal />
    </Modal>
  );
}
