"use client";

import { useCustomer } from "@/hooks/use-customer";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { useShoppingCart } from "@/hooks/use-shopping-cart";
import { updateCompany } from "@/lib/api/company";
import { createPaymentMethod } from "@/lib/api/payment";
import { CardNumberElement, useElements, useStripe } from "@stripe/react-stripe-js";
import { useTranslations } from "next-intl";
import { enqueueSnackbar } from "notistack";
import { useFormContext } from "react-hook-form";

import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { BillingFormData } from "./journey-billing-provider";

export function JourneyBillingSubmit() {
  const t = useTranslations("shop.common.journey.billing");
  const form = useFormContext<BillingFormData>();
  const stripe = useStripe();
  const elements = useElements();
  const { customer } = useCustomer();
  const { updateCart } = useShoppingCart();

  const { deleteParam } = useQueryFilter(["edit"]);

  const customerCompany = customer?.company;

  async function handleFormSubmit(data: BillingFormData) {
    try {
      if (!customer) return;
      if (!stripe || !elements) return;

      if (!data.usePersonalDataOnBilling) {
        if (
          !data.billing?.fullName ||
          !data.billing?.companyName ||
          !data.billing?.streetAndNumber ||
          !data.billing?.zipCode ||
          !data.billing?.city
        ) {
          enqueueSnackbar(t("billingInformation.mandatoryFields"), { variant: "error" });
          return;
        }

        await updateCompany(customer.company.id, {
          billing: {
            full_name: data?.billing?.fullName,
            country_code: data?.billing?.countryCode,
            country_name: data?.billing?.countryName,
            company_name: data?.billing?.companyName,
            street_and_number: data?.billing?.streetAndNumber,
            city: data?.billing?.city,
            zip_code: data?.billing?.zipCode,
          },
        });
      }

      const paymentMethodId = data.paymentMethodId;
      const paymentMethodType = data.paymentMethodType;

      deleteParam("edit");

      if (paymentMethodType === "CREDIT_CARD" && !paymentMethodId) {
        const { paymentMethod: stripePaymentMethod, error } = await stripe.createPaymentMethod({
          type: "card",
          card: elements.getElement(CardNumberElement)!,
          billing_details: {
            name: `${data.card?.firstName} ${data.card?.lastName}`,
            email: customer.email,
            address: {
              city: data.billing?.city || customerCompany?.billing?.city || undefined,
              country: data.billing?.countryCode || customerCompany?.billing?.country_code || undefined,
              line1: data.billing?.streetAndNumber || customerCompany?.billing?.street_and_number || undefined,
              postal_code: data.billing?.zipCode || customerCompany?.billing?.zip_code || undefined,
            },
          },
        });

        if (error || !stripePaymentMethod) {
          throw new Error(error?.message || "Failed to set payment method");
        }

        const paymentMethodResponse = await createPaymentMethod({
          customer_id: customer.id,
          platform: "stripe",
          platform_payment_method_id: stripePaymentMethod.id,
          type: "CREDIT_CARD",
          card_last_4: stripePaymentMethod.card?.last4,
          card_brand: stripePaymentMethod.card?.brand,
          card_country: stripePaymentMethod.card?.country || "",
          saved_for_future_purchase: data.saveForFuturePurchase || false,
        });

        if (!paymentMethodResponse.success) {
          console.error(paymentMethodResponse.error);
          throw new Error("Failed to create payment method");
        }

        await updateCart({
          payment: {
            payment_method_id: paymentMethodResponse.data.id,
            payment_method_type: "CREDIT_CARD",
          },
        });
        return;
      }

      await updateCart({
        payment: {
          payment_method_id: paymentMethodId,
          payment_method_type: paymentMethodType,
        },
      });
    } catch (error) {
      console.error("Payment error:", error);
      enqueueSnackbar(t("paymentMethodError"), { variant: "error" });
    }
  }

  return (
    <Button
      onClick={form.handleSubmit(handleFormSubmit)}
      disabled={form.formState.isSubmitting}
      color="yellow"
      variant="filled"
      size="large"
    >
      {form.formState.isSubmitting ? t("submitButton.loading") : t("submitButton.label")}
    </Button>
  );
}
