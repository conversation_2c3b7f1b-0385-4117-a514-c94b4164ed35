"use client";

import React, { useState } from "react";
import { JourneyResumeItem } from "./journey-resume-item";
import { JourneyThirdParty } from "../journey-third-party";
import { formatCurrency } from "@/utils/formatCurrency";
import { useShoppingCart } from "@/hooks/use-shopping-cart";
import { useTranslations } from "next-intl";
import { Skeleton } from "@/components/ui/skeleton";

export function JourneyResume({ children }: { children?: React.ReactNode }) {
  const t = useTranslations("shop.common.resume");
  const { shoppingCart, isUpdatingCart } = useShoppingCart();

  const [showMore, setShowMore] = useState(false);

  const handleShowMoreClick = () => {
    setShowMore(!showMore);
  };

  const showThirdPartyCosts = ["LONG", "QUICK_LICENSE"].includes(shoppingCart.journey);

  return (
    <div className="flex flex-row items-center relative">
      <div className="flex flex-col gap-6 drop-shadow-md bg-white w-full px-4 py-6 md:p-7 md:pr-10 rounded-4xl">
        <div className="flex flex-row justify-between items-center">
          <p className="text-primary text-3xl font-bold">{t("resume")}</p>
          {!!shoppingCart.items.length && (
            <p onClick={handleShowMoreClick} className="text-sm text-support-blue font-bold cursor-pointer">
              {showMore ? t("showLess") : t("showMore")}
            </p>
          )}
        </div>
        {showMore && (
          <div className="flex flex-col gap-6">
            {isUpdatingCart &&
              [...Array(shoppingCart.items.length || 1)].map((_, i) => (
                <Skeleton key={i} className="h-24 w-full rounded-2xl" />
              ))}
            {!isUpdatingCart && shoppingCart.items.map((item) => <JourneyResumeItem key={item.id} cartItem={item} />)}
          </div>
        )}
        {showMore && children}
        {!isUpdatingCart && (
          <div className="flex flex-col rounded-xl">
            {!!shoppingCart.vat_percentage && (
              <div className="flex flex-row justify-between p-4 rounded-t-xl border-[1px] border-error-container border-b-0 ">
                <p className="text-sm text-primary font-semibold">Vat {shoppingCart.vat_percentage || 0}%</p>
                <p className="text-sm text-primary font-bold">{formatCurrency(shoppingCart.vat_value)}</p>
              </div>
            )}
            <div className="flex flex-row justify-between bg-tonal-dark-blue-90 p-4 rounded-b-xl">
              <div className="flex flex-col gap-1">
                <p className="text-primary font-bold">{t("netPrice")}</p>
                <p className="text-sm text-tonal-dark-cream-20 italic">{t("payTimeInfo")}</p>
              </div>
              <p className="text-xl text-primary font-bold">{formatCurrency(shoppingCart.total)}</p>
            </div>
          </div>
        )}
        {isUpdatingCart && (
          <div className="flex flex-row justify-between bg-tonal-dark-blue-90 p-4 gap-2 rounded-b-xl">
            <div className="space-y-1">
              <Skeleton className="h-5 w-40" />
            </div>
            <Skeleton className="h-5 w-20" />
          </div>
        )}
        {showMore && showThirdPartyCosts && <JourneyThirdParty />}
      </div>
      <div className="hidden md:block">
        <div className="absolute top-[40px]">
          <div className="bg-tonal-dark-blue-90 w-12 h-12 rounded-full absolute z-10 my-auto  right-2 top-0 bottom-0"></div>
          <svg
            className="drop-shadow-[5px_2px_2px_rgba(0,0,0,0.05)]"
            width="40"
            height="128"
            viewBox="0 0 40 128"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M-1.52638e-06 0C-1.38053e-06 12.2314 7.07429 22.8064 17.3429 27.8331C17.3886 27.856 17.2971 27.8101 17.3429 27.8331C30.6743 34.3385 40 48.129 40 64C40 79.871 30.6743 93.6615 17.3429 100.167C17.2971 100.19 17.3886 100.144 17.3429 100.167C7.07429 105.194 -1.45858e-07 115.769 0 128L-1.52638e-06 0Z"
              fill="white"
            />
          </svg>
        </div>
      </div>
    </div>
  );
}
