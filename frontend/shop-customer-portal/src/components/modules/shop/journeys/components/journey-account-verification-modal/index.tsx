"use client";

import { TokenInput } from "@/components/_common/modals/verify-account-modal/token-input";
import { Spinner } from "@/components/ui/loader";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { useShoppingCart } from "@/hooks/use-shopping-cart";
import { useRouter } from "@/i18n/navigation";
import { resendTokenEmail } from "@/lib/api/account";
import { TypeResendToken } from "@/lib/api/account/types";
import { MAILTO } from "@/utils/system-consts";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { East, Error } from "@arthursenno/lizenzero-ui-react/Icon";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import { useMutation } from "@tanstack/react-query";
import { jwtDecode } from "jwt-decode";
import { signIn } from "next-auth/react";
import { useTranslations } from "next-intl";
import { enqueueSnackbar } from "notistack";
import { useEffect, useState } from "react";
import { CgSpinnerAlt } from "react-icons/cg";

interface JourneyAccountVerificationModalProps {
  type: "CODE" | "MAGIC-LINK";
  resendTokenType: TypeResendToken;
  email: string;
}

export function JourneyAccountVerificationModal({
  type,
  email,
  resendTokenType,
}: JourneyAccountVerificationModalProps) {
  const router = useRouter();
  const { shoppingCart, updateCart } = useShoppingCart();

  const { paramValues, deleteAllParams } = useQueryFilter(["verify-account", "magic"]);

  const [typedToken, setTypedToken] = useState<string>("");

  const t = useTranslations("shop.common.journey.createAccount.verificationModal");

  const magicLinkData = (() => {
    try {
      const magicLink = paramValues.magic;

      if (!magicLink) return null;

      const data = jwtDecode<{ token: string; email: string }>(magicLink);

      if (!data.token) return null;

      return data;
    } catch {
      return null;
    }
  })();

  const isModalOpen = !!paramValues["verify-account"] || !!magicLinkData;

  const isCodeFromMagicLink = !!magicLinkData?.token;

  const {
    mutate: validateCode,
    isPending: isValidatingCode,
    error: validateCodeError,
    isSuccess: isCodeValidated,
  } = useMutation({
    mutationFn: async ({ code, email }: { code: string; email: string }) => {
      try {
        if (!code[0] || !code[1] || !code[2] || !code[3] || !code[4]) {
          throw { message: t("errors.invalidCode") };
        }

        const response = await signIn("by-email", {
          token: code,
          email,
          redirect: false,
        });

        if (!response || !response.ok) {
          if (response?.status === 401) {
            throw { message: t("errors.invalidCode") };
          }

          throw { message: t("errors.failedToValidateCode") };
        }

        await updateCart({ email: email });

        if (shoppingCart.journey === "LONG") return router.push("./obligations");

        return router.push("./set-password");
      } catch (err) {
        throw err;
      }
    },
    onSuccess: () => {
      enqueueSnackbar(t("success.codeValidation"), { variant: "success" });
    },
    onError: (error) => {
      enqueueSnackbar(error.message || t("errors.failedToValidateCode"), { variant: "error" });
    },
  });

  const { mutate: sendVerificationEmail, isPending: isSendingVerificationEmail } = useMutation({
    mutationFn: async (email: string) => {
      try {
        const response = await resendTokenEmail(email, resendTokenType);
        const successfulResponseStatus = [200, 201];

        if (!response || !successfulResponseStatus.includes(response.status)) {
          throw { message: t("errors.failedToSendVerificationEmail") };
        }
      } catch (err) {
        throw err;
      }
    },
    onSuccess: () => {
      enqueueSnackbar(t("success.emailSent"), { variant: "success" });
    },
    onError: (error) => {
      enqueueSnackbar(error.message || t("errors.failedToSendVerificationEmail"), { variant: "error" });
    },
  });

  function handleValidateCode() {
    validateCode({ code: typedToken, email: email || magicLinkData?.email || "" });
  }

  function handleResendToken() {
    sendVerificationEmail(email || magicLinkData?.email || "");
  }

  function handleOnOpenChange(open: boolean) {
    if (!open) deleteAllParams();
  }

  useEffect(() => {
    if (!isCodeFromMagicLink) return;

    validateCode({ code: magicLinkData.token, email: magicLinkData.email });
  }, []);

  return (
    <Modal
      open={isModalOpen}
      onOpenChange={handleOnOpenChange}
      className="z-50 w-full"
      data-validating-code={isValidatingCode}
      style={{ maxWidth: "800px", borderRadius: "52px" }}
    >
      <div className="p-5">
        <div className="text-primary">
          {!isValidatingCode && !isCodeValidated && (
            <div className="mb-5 space-y-4 font-base">
              <h1 className="text-support-blue font-medium text-2xl mb-2">
                {type === "CODE" ? "Verify your account" : "Verify your email"}
              </h1>
              {type === "CODE" && (
                <>
                  <div
                    className="font-light inline"
                    dangerouslySetInnerHTML={{ __html: t.raw("description").replaceAll(`{email}`, email) }}
                  />
                  <p>{t("enterBelow")}</p>
                  <TokenInput setToken={setTypedToken} error={validateCodeError?.message} />
                  {!!validateCodeError && (
                    <div className="flex gap-2 items-center text-error">
                      <Error width={20} height={20} className="fill-error flex" />
                      <span>{validateCodeError?.message || t("errors.failedToValidateCode")}.</span>
                    </div>
                  )}
                </>
              )}
              {type === "MAGIC-LINK" && (
                <>
                  <div
                    className="font-light inline"
                    dangerouslySetInnerHTML={{ __html: t.raw("description").replaceAll(`{email}`, email) }}
                  />
                  <p className="font-light">{t("ifNotReceived")}</p>
                </>
              )}
              <div>
                <Button
                  variant="text"
                  color="dark-blue"
                  size="small"
                  className="inline-block"
                  onClick={handleResendToken}
                  trailingIcon={
                    isSendingVerificationEmail ? <CgSpinnerAlt className="animate-spin text-primary" /> : undefined
                  }
                >
                  {t("buttons.sendAgain")}
                </Button>
                <span>{t("moreInformation")} </span>
                <a href={MAILTO}> {t("supportCenter")}</a>
              </div>
              {type === "CODE" && (
                <div className="w-full flex justify-end mt-5">
                  <div>
                    <Button
                      type="button"
                      disabled={isValidatingCode}
                      color="yellow"
                      variant="filled"
                      size="small"
                      onClick={handleValidateCode}
                      trailingIcon={!isValidatingCode && <East />}
                    >
                      {isValidatingCode ? t("buttons.codeValidation.loading") : t("buttons.codeValidation.label")}
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
          {(isValidatingCode || isCodeValidated) && (
            <div className="flex flex-col items-center justify-center gap-4 py-10">
              <Spinner size="md" />
              <p>{t("validatingVerificationCode")}</p>
            </div>
          )}
        </div>
      </div>
    </Modal>
  );
}
