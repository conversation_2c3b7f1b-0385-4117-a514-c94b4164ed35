"use client";

import { cn } from "@/lib/utils";
import { validateLUCIDNumber } from "@/utils/validateLucidNumber";
import { Check } from "@arthursenno/lizenzero-ui-react/Icon";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { Link, useRouter } from "@/i18n/navigation";
import { useState } from "react";
import { useFormContext } from "react-hook-form";
import { JourneyInformationFormData } from "./journey-information-form-provider";
import { QuestionTooltip, QuestionTooltipDescription } from "@/components/_common/question-tooltip";
import { useTranslations } from "next-intl";
import { useShoppingCart } from "@/hooks/use-shopping-cart";
import { Skeleton } from "@/components/ui/skeleton";

const badgeStyleBase = "flex items-center rounded-lg px-2 text-xs font-bold w-fit min-h-[32px] self-end";
const badgeStyleError = "bg-error-opacity text-on-error-container";
const badgeStyleSuccess = "bg-success-container text-on-success-container";

interface JourneyInformationFormPersonalProps {}

export function JourneyInformationFormLucid({}: JourneyInformationFormPersonalProps) {
  const router = useRouter();
  const globalT = useTranslations("global");
  const t = useTranslations("shop.common.journey.information");

  const [isNumberValid, setIsNumberValid] = useState(true);

  const {
    register,
    formState: { errors, isSubmitting },
    clearErrors,
    watch,
  } = useFormContext<JourneyInformationFormData>();

  const { isUpdatingCart } = useShoppingCart();

  const hasErrors: boolean = !isNumberValid || !!errors.lucidNumber;
  const errorMessage = errors.lucidNumber ? errors.lucidNumber.message : globalT("inputs.lucid.errors.pleaseEnter");

  const badgeStyle = cn(badgeStyleBase, hasErrors ? badgeStyleError : badgeStyleSuccess);
  const badgeLabel = hasErrors ? globalT("inputs.lucid.errors.invalid") : globalT("inputs.lucid.valid");

  const handleLucidNumberChange = (value: any) => {
    const uppercasedValue = value.toUpperCase();
    if (value.length === 0) {
      clearErrors("lucidNumber");
      setIsNumberValid(true);
      return;
    }
    const isValid = validateLUCIDNumber(uppercasedValue);
    setIsNumberValid(isValid);
  };

  const companyLucid = watch("lucidNumber");

  if (isSubmitting) return <JourneyInformationFormLucidSkeleton />;

  return (
    <div className="w-full rounded-[32px] items-start bg-surface-02 flex flex-col px-4 py-6 md:py-7 md:px-8">
      <div className="flex flex-col md:flex-row w-full justify-between">
        <div className="flex items-center gap-3">
          <p className="text-primary font-medium text-xl mb-2">{t("lucid.title")}</p>
          <QuestionTooltip className="fill-primary">
            <QuestionTooltipDescription>{t("lucid.tooltip")}</QuestionTooltipDescription>
          </QuestionTooltip>
        </div>
        {companyLucid && <div className={badgeStyle}>{badgeLabel}</div>}
      </div>
      <Input
        label={globalT("inputs.lucid.label")}
        maxLength={17}
        placeholder={globalT("inputs.lucid.placeholder")}
        {...register("lucidNumber", {
          onChange: (e) => handleLucidNumberChange(e.target.value),
          value: companyLucid,
        })}
        variant={!isNumberValid && "error"}
        errorMessage={hasErrors && errorMessage}
        rightIcon={!hasErrors && companyLucid && <Check width={20} height={20} className="fill-tonal-green-40" />}
        enabled={!isUpdatingCart}
      />
      {errorMessage === globalT("inputs.lucid.errors.alreadyRegistered") && (
        <Link href="/auth/login" className="text-support-blue cursor-pointer text-sm underline">
          {t("tryLogin")}
        </Link>
      )}
    </div>
  );
}

export function JourneyInformationFormLucidSkeleton() {
  return (
    <div className="w-full rounded-[32px] items-start bg-surface-02 flex flex-col px-4 py-6 md:px-8 md:py-7 gap-5">
      <div className="flex">
        <p className="text-primary font-medium text-xl mb-2">Enter a valid LUCID number</p>
      </div>
      <div className="w-full">
        <Skeleton className="h-4 w-16 mb-2" />
        <Skeleton className="h-10 w-full" />
      </div>
    </div>
  );
}
