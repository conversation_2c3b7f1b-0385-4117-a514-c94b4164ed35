import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Paypal<PERSON>ogo } from "@arthursenno/lizenzero-ui-react/Figure";

export const PAYMENT_METHODS = [
  {
    label: "Alipay",
    value: "ALIPAY",
    icons: ["alipay.svg"] as string[],
  },
  {
    label: "PayPal",
    value: "PAYPAL",
    icons: ["paypal.svg"] as string[],
  },
  {
    label: "Klarna",
    value: "KLARNA",
    icons: ["klarna.png"] as string[],
  },
  {
    label: "Eps",
    value: "EPS",
    icons: ["eps.png"] as string[],
  },
  {
    label: "iDeal",
    value: "IDEAL",
    icons: ["ideal.png"] as string[],
  },
];
