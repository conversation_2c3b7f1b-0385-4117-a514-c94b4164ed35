"use client";

import { useRouter } from "@/i18n/navigation";
import { useFormContext } from "react-hook-form";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { JourneySelectCountriesFormData } from "./journey-select-countries-provider";
import { useSession } from "next-auth/react";
import { East } from "@arthursenno/lizenzero-ui-react/Icon";
import { useEffect, useState } from "react";
import { useShoppingCart } from "@/hooks/use-shopping-cart";
import { SaveProgress } from "@/components/ui/btn-save-my-progress";
import { useTranslations } from "next-intl";

export function JourneySelectCountriesSubmit() {
  const { status } = useSession();
  const router = useRouter();
  const t = useTranslations("shop.longJourney.selectCountries.submit");

  const [showError, setShowError] = useState(false);
  const { shoppingCart } = useShoppingCart();

  const {
    trigger,
    formState: { errors, isSubmitted },
    setError,
  } = useFormContext<JourneySelectCountriesFormData>();

  const customerCommitments = shoppingCart.customer_commitments;

  async function handleNextStep() {
    const licenseItems = shoppingCart.items.filter((i) => i.service_type === "EU_LICENSE");

    const erroCommitmentFind = licenseItems.find(
      (licenseItem) => !customerCommitments.find((c) => c.country_code === licenseItem.country_code)
    );

    if (!!erroCommitmentFind) {
      return setError(`items.[${erroCommitmentFind.country_code}]`, { message: "Commitment Assessment required" });
    }

    const isError = !shoppingCart.items.length;

    if (isError) return setShowError(true);

    const isValid = await trigger("items");
    if (!isValid) return;

    if (status === "unauthenticated") return router.push("./create-account");

    router.push("./obligations");
  }

  const errorMessage = (() => {
    if (!isSubmitted || !errors) return null;

    if (errors.root?.message) return errors.root.message;

    const itemErrors = errors.items;

    if (!itemErrors || !itemErrors.length) return null;

    return t("errors.answerAllQuestions");
  })();

  useEffect(() => {
    setShowError(false);
  }, [shoppingCart.items]);

  return (
    <div className="space-y-8 md:space-y-4">
      <p className="text-primary">{t("description")}</p>
      {!!errorMessage && <p className="text-right text-error">{errorMessage}</p>}
      {showError && <p className="text-error text-right block">{t("errors.selectValidCountry")}</p>}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-12 items-center">
        <SaveProgress />
        <Button
          color={errorMessage ? "red" : "yellow"}
          variant="filled"
          size="medium"
          disabled={!!errorMessage || showError}
          onClick={handleNextStep}
          trailingIcon={<East />}
        >
          {t("label")}
        </Button>
      </div>
    </div>
  );
}
