"use client";

import { CountryIcon } from "@/components/_common/country-icon";
import { QuestionTooltip, QuestionTooltipDescription } from "@/components/_common/question-tooltip";
import { useShoppingCart } from "@/hooks/use-shopping-cart";
import { useTranslations } from "next-intl";

export function JourneyObligationCart({ obliged = false }: { obliged?: boolean }) {
  const t = useTranslations("shop.longJourney.obligations.card");
  const { shoppingCart } = useShoppingCart();

  const licenseItems = shoppingCart.items.filter((item) => item.service_type === "EU_LICENSE");

  const filteredItems = licenseItems.filter((item) => {
    const customerCommitment = shoppingCart.customer_commitments.find(
      (commitment) => commitment.country_code === item.country_code
    );
    const isItemObliged = customerCommitment?.is_license_required;

    if (obliged && isItemObliged) return true;

    if (!obliged && !isItemObliged) return true;

    return false;
  });

  return (
    <div className="w-full flex flex-col gap-6 justify-between md:w-2/4 px-4 py-6 md:p-10 rounded-2xl bg-surface-02">
      <div>
        <div className="flex items-center gap-3">
          <p className={`text-[28px] font-bold ${obliged ? "text-on-surface-04" : "text-tonal-dark-green-30"}`}>
            {obliged ? t("obliged.title") : t("notObliged.title")}
          </p>
          <QuestionTooltip>
            <QuestionTooltipDescription>
              {obliged ? t("obliged.description") : t("notObliged.description")}
            </QuestionTooltipDescription>
          </QuestionTooltip>
        </div>

        <p className="mt-5 text-sm text-tonal-dark-cream-30">
          {t("preResult")}{" "}
          <span className={`font-bold ${obliged ? "text-on-surface-04" : "text-success"}`}>
            {obliged ? t("obliged.title") : t("notRequired.title")}
          </span>{" "}
          {t("postResult")}
        </p>

        <div className="mt-6 flex items-center gap-3">
          {!!filteredItems.length &&
            filteredItems.map((item) => (
              <CountryIcon
                key={item.country_code}
                country={{
                  flag_url: item.country_flag,
                  name: item.country_name,
                }}
                className="size-8"
              />
            ))}
        </div>
      </div>

      <p className="text-sm text-tonal-dark-cream-30 underline">{t("moreAboutLegalObligations")}</p>
    </div>
  );
}
