"use client";
import { Divider } from "@/components/_common/divider";
import { TooltipIcon } from "@/components/_common/tooltipIcon";
import { ShopContent } from "@/components/modules/shop/components/shop-content";
import { usePricing } from "@/hooks/use-pricing";
import { useShoppingCart } from "@/hooks/use-shopping-cart";
import { useRouter } from "@/i18n/navigation";
import { ContractType } from "@/lib/api/contracts/types";
import { PriceList, ShoppingCartItem } from "@/lib/api/shoppingCart/types";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { East } from "@arthursenno/lizenzero-ui-react/Icon";
import { useTranslations } from "next-intl";
import { enqueueSnackbar } from "notistack";
import { useEffect, useState } from "react";
import { PackagingObligation } from "./packaging-obligation";
import { ServiceCard } from "./service-card";

export function JourneySelectServices() {
  const router = useRouter();
  const t = useTranslations("shop.longJourney.selectServices");
  const [isRedirecting, setIsRedirecting] = useState(false);

  const { shoppingCart, updateCartItems } = useShoppingCart();

  const [selectedLicenses, setSelectedLicenses] = useState<ShoppingCartItem[]>([]);
  const [selectedActionGuides, setSelectedActionGuides] = useState<
    { country_code: string; service_type: ContractType; price_list: PriceList }[]
  >([]);

  const { priceLists: actionGuidePriceLists, isLoading: isLoadingActionGuidePriceList } = usePricing({
    serviceType: "ACTION_GUIDE",
    year: new Date().getFullYear(),
  });

  const licenseFromPrice = Math.min(
    ...shoppingCart.items
      .filter((item) => item.service_type === "EU_LICENSE")
      .map((item) => item.price_list.registration_fee || 0)
  );
  const actionGuidePriceList = actionGuidePriceLists?.[0];
  const actionGuideFromPrice = actionGuidePriceList?.price || 0;

  const [selectedServices, setSelectedServices] = useState<Record<ContractType, boolean>>({
    EU_LICENSE: false,
    ACTION_GUIDE: false,
    DIRECT_LICENSE: false,
  });

  function handleToggleService(serviceType: ContractType) {
    setSelectedServices((prevSelectedService) => ({
      ...prevSelectedService,
      [serviceType]: !prevSelectedService[serviceType],
    }));
  }

  function handleToggleItem(countryCode: string, serviceType: ContractType) {
    const cartItem = shoppingCart.items.find((item) => item.country_code === countryCode);

    if (!cartItem || !actionGuidePriceList) return;

    if (serviceType === "ACTION_GUIDE") {
      setSelectedActionGuides((previous) => {
        const alreadySelected = previous.find((item) => item.country_code === countryCode);

        if (alreadySelected) return previous.filter((item) => item.country_code !== countryCode);

        return [
          ...previous,
          { country_code: countryCode, service_type: "ACTION_GUIDE", price_list: actionGuidePriceList },
        ];
      });
      return;
    }

    if (serviceType === "EU_LICENSE") {
      setSelectedLicenses((previous) => {
        const alreadySelected = previous.find((item) => item.country_code === countryCode);

        if (alreadySelected) return previous.filter((item) => item.country_code !== countryCode);

        return [...previous, cartItem];
      });
      return;
    }
  }

  async function handleConfirmation() {
    if (!selectedServices.EU_LICENSE && !selectedServices.ACTION_GUIDE) {
      enqueueSnackbar(t("errors.noServices"), { variant: "error" });
      return;
    }

    if (selectedServices.EU_LICENSE && !selectedLicenses.length) {
      enqueueSnackbar(t("errors.noServices"), { variant: "error" });
      return;
    }

    if (selectedServices.ACTION_GUIDE && !selectedActionGuides.length) {
      enqueueSnackbar(t("errors.noCountries"), { variant: "error" });
      return;
    }

    const formattedLicenses = selectedServices.EU_LICENSE ? selectedLicenses : [];
    const formattedActionGuides = selectedServices.ACTION_GUIDE
      ? selectedActionGuides.map((item) => {
          const actionGuideItem = shoppingCart.items.find(
            (i) => i.service_type === "ACTION_GUIDE" && i.country_code === item.country_code
          );

          if (!actionGuideItem) {
            const licenseItem = shoppingCart.items.find(
              (i) => i.service_type === "EU_LICENSE" && i.country_code === item.country_code
            )!;

            const formattedItem: ShoppingCartItem = {
              id: null as unknown as number,
              country_id: licenseItem.country_id,
              country_code: licenseItem.country_code,
              country_name: licenseItem.country_name,
              country_flag: licenseItem.country_flag,
              service_type: "ACTION_GUIDE",
              specification_type: "PURCHASE",
              year: new Date().getFullYear(),
              price_list: null as unknown as PriceList,
              price: 0,
            };

            return formattedItem;
          }

          return actionGuideItem;
        })
      : [];

    setIsRedirecting(true);

    await updateCartItems([...formattedLicenses, ...formattedActionGuides]);

    const nextPage = selectedServices.EU_LICENSE ? "./calculator" : "./shopping-cart";

    router.push(nextPage);
  }

  useEffect(() => {
    const licenses = shoppingCart.items.filter((item) => item.service_type === "EU_LICENSE");

    if (licenses.length) return;

    router.push("./select-countries");
  }, []);

  useEffect(() => {
    const licenses = shoppingCart.items.filter((item) => item.service_type === "EU_LICENSE");
    const actionGuides = shoppingCart.items.filter((item) => item.service_type === "ACTION_GUIDE");

    if (licenses.length) {
    }

    setSelectedLicenses(licenses);
    setSelectedActionGuides(actionGuides);
    setSelectedServices({
      EU_LICENSE: !!licenses.length,
      ACTION_GUIDE: !!actionGuides.length,
      DIRECT_LICENSE: false,
    });
  }, [shoppingCart.items]);

  const licenseTotalPrice = selectedLicenses.reduce((acc, item) => acc + (item.price_list.registration_fee || 0), 0);
  const actionGuideTotalPrice = selectedActionGuides.reduce((acc, item) => acc + (item.price_list.price || 0), 0);

  const licensingServiceContains: string[] = [
    "Phone, email and chat support in X languages",
    "Licensing, registration and quantity reporting in your target country - carried out by us",
    "Support for recycling and labeling requirements",
    "Completely digital process including digital contract conclusion",
    "Access to our platform with personal account, clear dashboard and all your documents",
    "Information service for changes",
    "Payment options: invoice, PayPal, direct debit, (...)",
  ];

  return (
    <ShopContent containerClassName="bg-surface-02 md:pb-32">
      <div className="grid grid-cols-1 lg:grid-cols-2 items-start gap-10 lg:gap-32">
        <ServiceCard
          initialCollapsible
          mountsChecked
          title={t("cardService.licensing.title")}
          subTitle={t("cardService.licensing.subTitle")}
          price={licenseFromPrice}
          contains={licensingServiceContains}
          popular
          onCheck={() => handleToggleService("EU_LICENSE")}
          checked={selectedServices.EU_LICENSE}
        />
        {selectedServices.EU_LICENSE && (
          <div>
            <div className="flex items-center gap-2 mb-6">
              <p className="text-primary font-bold text-2xl">{t("selectCountries")}</p>
              <TooltipIcon info={t("selectCountries")} />
            </div>
            <div className="flex flex-col gap-10">
              <PackagingObligation
                title={t("cardService.licensing.title")}
                serviceType="EU_LICENSE"
                selectedItems={selectedLicenses}
                onSelectItem={handleToggleItem}
                totalPrice={licenseTotalPrice}
                pricePerItem={licenseFromPrice}
              />
            </div>
            <Divider className="hidden md:block" style={{ marginTop: "10px", marginBottom: "24px" }} />

            {!selectedServices.ACTION_GUIDE && selectedServices.EU_LICENSE && (
              <div className="grid w-full max-md:mt-6">
                <Button
                  onClick={handleConfirmation}
                  disabled={!selectedServices.EU_LICENSE && !selectedServices.ACTION_GUIDE}
                  trailingIcon={<East />}
                  color="yellow"
                  size="medium"
                  variant="filled"
                >
                  {t("configureServices")}
                </Button>
              </div>
            )}
          </div>
        )}
      </div>

      <Divider />

      <div className="grid grid-cols-1 lg:grid-cols-2 items-start gap-10 lg:gap-32">
        <ServiceCard
          title={t("cardService.actionGuide.title")}
          subTitle={t("cardService.actionGuide.subTitle")}
          price={actionGuideFromPrice}
          contains={[]}
          onCheck={() => handleToggleService("ACTION_GUIDE")}
          checked={selectedServices.ACTION_GUIDE}
        />

        {selectedServices.ACTION_GUIDE && (
          <div>
            <div className="flex items-center gap-2 mb-6">
              <p className="text-primary font-bold text-2xl">{t("selectCountries")}</p>
              <TooltipIcon info={t("selectCountries")} />
            </div>
            <div className="flex flex-col gap-10">
              <PackagingObligation
                serviceType="ACTION_GUIDE"
                selectedItems={selectedActionGuides}
                onSelectItem={handleToggleItem}
                totalPrice={actionGuideTotalPrice}
                pricePerItem={actionGuideFromPrice}
                title={t("actionGuide")}
              />
            </div>
            <Divider className="hidden md:block" style={{ marginTop: "10px", marginBottom: "24px" }} />

            <div className="grid w-full max-md:mt-6">
              <Button
                onClick={handleConfirmation}
                disabled={isRedirecting || (!selectedServices.EU_LICENSE && !selectedServices.ACTION_GUIDE)}
                trailingIcon={<East />}
                color="yellow"
                size="medium"
                variant="filled"
              >
                {t("configureServices")}
              </Button>
            </div>
          </div>
        )}
      </div>
    </ShopContent>
  );
}
