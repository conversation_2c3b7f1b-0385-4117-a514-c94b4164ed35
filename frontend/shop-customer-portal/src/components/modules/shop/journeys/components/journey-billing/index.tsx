"use client";

import { ShopTrustpilot } from "@/components/modules/shop/components/shop-trustpilot";
import { JourneyResume } from "@/components/modules/shop/journeys/components/journey-resume";
import { useTranslations } from "next-intl";
import { JourneyBillingBanner } from "./journey-billing-banner";
import { JourneyBillingForm } from "./journey-billing-form";
import { BillingProvider } from "./journey-billing-provider";
import { useShoppingCart } from "@/hooks/use-shopping-cart";
import { JourneyBillingSubmit } from "./journey-billing-submit";

export function JourneyBilling() {
  const t = useTranslations("shop.common.journey.billing");
  const { shoppingCart } = useShoppingCart();

  return (
    <div className="flex flex-col md:flex-row gap-6 w-full mb-20 ">
      <BillingProvider>
        <JourneyBillingForm />
        <div className="flex flex-col gap-6 md:gap-10 md:max-w-[40%]">
          <JourneyResume />

          {!shoppingCart.items.length && (
            <p className="text-base text-error text-center my-5">{t("selectOneCountry")}</p>
          )}

          <div className="flex flex-col gap-6 md:gap-8">
            <div className="grid grid-cols-1 md:grid-cols-2">
              <div className="hidden md:block"></div>
              <JourneyBillingSubmit />
            </div>
            <ShopTrustpilot />
          </div>
          <JourneyBillingBanner />
        </div>
      </BillingProvider>
    </div>
  );
}
