"use client";

import { MapCountries } from "@/components/_common/map";
import { MapBanner } from "@arthursenno/lizenzero-ui-react/Banner";
import { Error } from "@arthursenno/lizenzero-ui-react/Icon";

import { CountryRecommendationModalTrigger } from "@/components/_common/modals/country-recommendation-modal/country-recommendation-modal-trigger";
import { useLiberatedCountries } from "@/hooks/use-liberated-countries";
import { useShoppingCart } from "@/hooks/use-shopping-cart";
import { useCustomer } from "@/hooks/use-customer";
import { useFormContext } from "react-hook-form";

import { JourneySelectCountriesFormData } from "./journey-select-countries-provider";
import { useTranslations } from "next-intl";

export function JourneySelectCountriesMap() {
  const t = useTranslations("shop.longJourney.selectCountries.map");
  const { shoppingCart, addCartItem, deleteCartItem, isUpdatingCart } = useShoppingCart();

  const { liberatedCountries } = useLiberatedCountries();

  const { customer } = useCustomer();

  const contractedCountryCodes = (() => {
    if (!customer) return [];

    if (shoppingCart.journey === "QUICK_ACTION_GUIDE") {
      const actionGuideContract = customer.contracts.find((c) => c.type === "ACTION_GUIDE");

      if (!actionGuideContract) return [];

      return actionGuideContract.action_guides.map((c) => c.country_code);
    }

    const euLicenseContract = customer.contracts.find((c) => c.type === "EU_LICENSE");

    if (!euLicenseContract) return [];

    return euLicenseContract.licenses.map((l) => l.country_code);
  })();

  const { reset, clearErrors } = useFormContext<JourneySelectCountriesFormData>();

  async function handleSelectCountry(countryCode: string) {
    if (isUpdatingCart) return;

    const selectCountry = liberatedCountries.find((country) => country.code === countryCode);

    if (!selectCountry) return;

    const alreadyAdded = shoppingCart.items.find((i) => i.country_code === countryCode);

    if (!!alreadyAdded) {
      if (shoppingCart.items.length - 1 === 0) reset({ items: {} });

      await deleteCartItem(alreadyAdded.id);

      clearErrors(`items.${alreadyAdded.country_code}`);

      return;
    }

    await addCartItem(selectCountry.code, "EU_LICENSE");
  }

  const selectedCountries = shoppingCart.items
    .filter((i) => i.service_type === "EU_LICENSE")
    .map((item) => item.country_code);

  return (
    <MapCountries
      className="hidden md:block aspect-square h-auto"
      onSelectCountry={handleSelectCountry}
      selectedCountries={selectedCountries}
      liberatedCountries={liberatedCountries}
      contractedCountryCodes={contractedCountryCodes}
    >
      <div className="absolute top-0 mt-3 ml-3">
        <MapBanner title={t("banner.benefitCommunication")} style={{ width: "100%", zIndex: 1000 }}>
          <Error width={"6%"} height={"6%"} className="fill-primary mr-2.5 min-w-6 min-h-6 max-w-[50px] max-h-[50px]" />
          <p>
            {t("banner.title")} <CountryRecommendationModalTrigger className="text-primary" recommendation="" />
          </p>
        </MapBanner>
      </div>
    </MapCountries>
  );
}
