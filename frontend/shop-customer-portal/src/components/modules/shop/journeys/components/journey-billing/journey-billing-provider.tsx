"use client";

import { CITY_REGEX, ZIP_CODE_REGEX } from "@/utils/regex";
import { ADDRESS_REGEX, COMPANY_NAME_REGEX, SPECIAL_CHARS_REGEX } from "@/utils/regex";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { FormProvider, useForm } from "react-hook-form";
import { z } from "zod";
import { PAYMENT_METHODS_KEYS } from "./payment-methods";

const billingFormSchema = (t: any) => {
  return z.object({
    paymentMethodType: z.enum([PAYMENT_METHODS_KEYS[0], ...PAYMENT_METHODS_KEYS]).default(PAYMENT_METHODS_KEYS[0]),
    paymentMethodId: z.string().optional(),
    card: z
      .object({
        firstName: z
          .string()
          .trim()
          .min(2, t("validation.minimumLength", { min: 2 }))
          .refine((value) => SPECIAL_CHARS_REGEX.test(value), {
            message: t("validation.specialCharacters"),
          }),
        lastName: z
          .string()
          .trim()
          .min(2, t("validation.minimumLength", { min: 2 }))
          .refine((value) => SPECIAL_CHARS_REGEX.test(value), {
            message: t("validation.specialCharacters"),
          }),
      })
      .optional(),
    saveForFuturePurchase: z.boolean().default(false),
    usePersonalDataOnBilling: z.boolean().default(true),
    billing: z
      .object({
        fullName: z
          .string({
            required_error: t("validation.required"),
          })
          .trim()
          .max(50, t("validation.maximumLength", { max: 50 }))
          .refine((value) => COMPANY_NAME_REGEX.test(value), {
            message: t("validation.specialCharacters"),
          })
          .optional(),
        countryCode: z
          .string({
            required_error: t("validation.required"),
          })
          .trim(),
        countryName: z
          .string({
            required_error: t("validation.required"),
          })
          .trim(),
        companyName: z
          .string({
            required_error: t("validation.required"),
          })
          .trim()
          .max(50, t("validation.maximumLength", { max: 50 }))
          .refine((value) => COMPANY_NAME_REGEX.test(value), {
            message: t("validation.specialCharacters"),
          })
          .optional(),
        streetAndNumber: z
          .string({
            required_error: t("validation.required"),
          })
          .trim()
          .max(50, t("validation.maximumLength", { max: 50 }))
          .refine((value) => ADDRESS_REGEX.test(value), {
            message: t("validation.specialCharacters"),
          }),
        city: z
          .string()
          .trim()
          .regex(CITY_REGEX, t("validation.specialCharacters"))
          .min(1, { message: t("validation.minimumLength", { min: 1 }) }),
        zipCode: z
          .string({
            required_error: t("validation.required"),
          })
          .trim()
          .max(10, {
            message: t("validation.maximumLength", { max: 10 }),
          })
          .refine((value) => ZIP_CODE_REGEX.test(value), {
            message: t("validation.specialCharacters"),
          }),
      })
      .optional(),
  });
};

export type BillingFormData = z.infer<ReturnType<typeof billingFormSchema>>;

export const useBillingFormSchema = () => {
  const t = useTranslations("global");

  return billingFormSchema(t);
};

export function BillingProvider({ children }: { children: React.ReactNode }) {
  const form = useForm<BillingFormData>({
    resolver: zodResolver(useBillingFormSchema()),
    mode: "all",
  });

  return <FormProvider {...form}>{children}</FormProvider>;
}
