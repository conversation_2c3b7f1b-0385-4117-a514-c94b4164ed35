/* eslint-disable @next/next/no-img-element */
import { QuestionTooltip, QuestionTooltipDescription } from "@/components/_common/question-tooltip";
import { CheckCircle, Delete, KeyboardArrowDown, KeyboardArrowUp } from "@arthursenno/lizenzero-ui-react/Icon";
import Image from "next/image";
import { useEffect, useState } from "react";

interface JourneySelectCountriesModalItemProps {
  name: string;
  img: string;
  tooltipInfo?: string;
  onDel?: () => void;
  children?: React.ReactNode;
  sizeIconDel?: number;
  countryChecked?: boolean;
  onCountryChecked?: () => void;
}

export function JourneySelectCountriesModalItem({
  name,
  img,
  tooltipInfo,
  onDel,
  children,
  sizeIconDel = 24,
  countryChecked,
  onCountryChecked,
}: JourneySelectCountriesModalItemProps) {
  const [isOpen, setIsOpen] = useState(false);

  const handleCollapsible = () => {
    setIsOpen((current) => !current);
  };

  useEffect(() => {
    if (countryChecked) {
      setIsOpen(false);
      onCountryChecked && onCountryChecked();
    }
  }, [countryChecked]);

  return (
    <div className="px-7 py-5 rounded-[32px] bg-background w-full">
      <div className="flex items-center justify-between w-full">
        <div className="flex items-center">
          {onDel && (
            <button onClick={onDel}>
              <Delete width={sizeIconDel} height={sizeIconDel} className="fill-primary" />
            </button>
          )}

          <Image src={img} alt="country" className="w-7 h-7 rounded-full ml-2 mr-3" width={28} height={28} />

          <p className="text-xl font-bold text-primary">{name}</p>

          {countryChecked && <CheckCircle className="fill-success size-5 ml-3" />}
        </div>

        <div className="flex flex-row gap-1">
          {tooltipInfo && (
            <QuestionTooltip>
              <QuestionTooltipDescription>{tooltipInfo}</QuestionTooltipDescription>
            </QuestionTooltip>
          )}

          {children && (
            <button onClick={handleCollapsible}>
              {isOpen ? (
                <KeyboardArrowUp width={24} height={24} className="fill-primary" />
              ) : (
                <KeyboardArrowDown width={24} height={24} className="fill-primary" />
              )}
            </button>
          )}
        </div>
      </div>

      {isOpen && <div className="mt-9">{children}</div>}
    </div>
  );
}
