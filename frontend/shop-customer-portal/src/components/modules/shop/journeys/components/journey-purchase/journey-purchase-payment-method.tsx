import { Skeleton } from "@/components/ui/skeleton";
import { PaymentMethodType } from "@/lib/api/payment/types";
import { useRouter } from "@/i18n/navigation";
import { getPaymentMethodById } from "@/lib/api/payment";
import { useQuery } from "@tanstack/react-query";
import { FaCheckSquare, FaRegSquare } from "react-icons/fa";
import { useTranslations } from "next-intl";
import { PAYMENT_METHODS } from "../journey-billing/payment-methods";
import { CardBrand } from "@/components/_common/card-brand";

interface JourneyPurchasePaymentMethodProps {
  paymentMethodId?: string;
  paymentMethodType?: PaymentMethodType;
  usePersonalData: boolean;
}

export function JourneyPurchasePaymentMethod({
  usePersonalData,
  paymentMethodId,
  paymentMethodType,
}: JourneyPurchasePaymentMethodProps) {
  const router = useRouter();
  const t = useTranslations("shop.common.journey.purchase.paymentMethod");

  const formatPaymentMethod = (paymentMethod?: PaymentMethodType) => {
    switch (paymentMethod) {
      case "CREDIT_CARD":
        return t("methods.creditCard");
      case "PAYPAL":
        return t("methods.paypal");
      case "ALIPAY":
        return t("methods.alipay");
      case "EPS":
        return t("methods.eps");
      case "IDEAL":
        return t("methods.ideal");
      case "KLARNA":
        return t("methods.klarna");
      case "INVOICE":
        return t("methods.invoice");
      default:
        return "";
    }
  };

  const { data: paymentMethod, isLoading: paymentMethodLoading } = useQuery({
    queryKey: ["payment-method", paymentMethodId],
    queryFn: () => getPaymentMethodById(paymentMethodId!),
    enabled: Boolean(paymentMethodId),
  });

  const paymentMethodTypeLabel = formatPaymentMethod(paymentMethodType);

  function handleRedirectToBilling() {
    router.push("./billing?edit=true");
  }

  return (
    <div className="px-4 py-6 md:p-6 bg-surface-01 rounded-3xl">
      <div className="flex justify-between items-center mb-6">
        <p className="text-primary text-2xl font-bold">{t("title")}</p>
        <button className="text-support-blue text-xl font-bold" onClick={handleRedirectToBilling}>
          {t("edit")}
        </button>
      </div>
      <div className="bg-background rounded-[20px] p-4 flex flex-col gap-3">
        {!paymentMethodLoading && (
          <>
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-1 py-1">
                {paymentMethodType !== "CREDIT_CARD" &&
                  PAYMENT_METHODS.find((method) => method.value === paymentMethodType)?.icons.map((icon, index) => (
                    <div
                      key={`${icon}-${index}`}
                      className="w-10 h-7 py-1 bg-tonal-dark-cream-96 rounded-sm flex items-center justify-center relative"
                    >
                      {/* eslint-disable-next-line @next/next/no-img-element */}
                      <img
                        src={`/assets/payment-methods/${icon}`}
                        alt={icon}
                        className="h-full w-auto object-contain"
                      />
                    </div>
                  ))}
                {paymentMethodType === "CREDIT_CARD" && <CardBrand brand={paymentMethod?.card_brand} />}
              </div>
              <p className="text-primary text-base font-bol">{paymentMethodTypeLabel}</p>
            </div>
            {paymentMethodType === "CREDIT_CARD" && (
              <p className="text-tonal-dark-cream-30 text-base font-bold">{paymentMethod?.card_last_4}</p>
            )}
          </>
        )}
        {paymentMethodLoading && (
          <>
            <Skeleton className="w-1/2 h-4" />
            <Skeleton className="w-16 h-4" />
          </>
        )}
      </div>
      <label className="mt-4 flex gap-2 items-start">
        {usePersonalData ? (
          <FaCheckSquare className="fill-on-surface-01 w-5 h-5" />
        ) : (
          <FaRegSquare className="fill-on-surface-01 w-5 h-5" />
        )}
        <p className="text-base text-grey-blue">{t("usePersonalData")}</p>
      </label>
    </div>
  );
}
