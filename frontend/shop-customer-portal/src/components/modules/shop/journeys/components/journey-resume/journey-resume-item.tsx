import { CountryIcon } from "@/components/_common/country-icon";
import { ShoppingCartItem } from "@/lib/api/shoppingCart/types";
import { calculateDirectLicenseNetValue } from "@/utils/calculate-germany-total-amount";
import { formatCurrency } from "@/utils/formatCurrency";

interface JourneyResumeItemProps {
  cartItem: ShoppingCartItem;
}

export function JourneyResumeItem({ cartItem }: JourneyResumeItemProps) {
  return (
    <div className="pt-3 px-4 pb-6 rounded-2xl border border-surface-03">
      <div>
        <div className="flex items-center py-4 gap-3">
          <CountryIcon country={{ flag_url: cartItem.country_flag, name: cartItem.country_name }} />
          <p className="text-primary font-bold mt-1">{cartItem.country_name}</p>
        </div>

        <hr className="text-on-surface-01 opacity-30" />
      </div>
      {cartItem.service_type === "EU_LICENSE" && (
        <>
          <div>
            <div className="flex items-center py-4 gap-3 justify-between">
              <p className="text-primary text-sm">Registration fee</p>
              <p className="text-primary text-sm font-bold flex-none">
                {formatCurrency(cartItem.price_list?.registration_fee || 0)}
              </p>
            </div>
            <hr className="text-on-surface-01 opacity-30" />
          </div>
          <div>
            <div className="flex items-center py-4 gap-3 justify-between">
              <p className="text-primary text-sm">Handling fee</p>
              <p className="text-primary text-sm font-bold flex-none">
                {formatCurrency(cartItem.price_list?.handling_fee || 0)}
              </p>
            </div>
            <hr className="text-on-surface-01 opacity-30" />
          </div>
        </>
      )}
      {cartItem.service_type === "ACTION_GUIDE" && (
        <div>
          <div className="flex items-center py-4 gap-3 justify-between">
            <p className="text-primary text-sm">Action guide</p>
            <p className="text-primary text-sm font-bold flex-none">
              {formatCurrency(cartItem.price_list?.price || 0)}
            </p>
          </div>
          <hr className="text-on-surface-01 opacity-30" />
        </div>
      )}
      {cartItem.service_type === "DIRECT_LICENSE" && (
        <div>
          <div className="flex items-center py-4 gap-3 justify-between">
            <p className="text-primary text-sm">Direct license</p>
            <p className="text-primary text-sm font-bold flex-none">{formatCurrency(cartItem.price)}</p>
          </div>
          <hr className="text-on-surface-01 opacity-30" />
        </div>
      )}
    </div>
  );
}
