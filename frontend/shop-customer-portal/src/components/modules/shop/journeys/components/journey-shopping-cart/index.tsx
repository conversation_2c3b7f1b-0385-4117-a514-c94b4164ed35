"use client";

import { CountryIcon } from "@/components/_common/country-icon";
import { Divider } from "@/components/_common/divider";
import { ShopTrustpilot } from "@/components/modules/shop/components/shop-trustpilot";
import { Spinner } from "@/components/ui/loader";
import { useJourney } from "@/hooks/use-journey";
import { useShoppingCart } from "@/hooks/use-shopping-cart";
import { Link, useRouter } from "@/i18n/navigation";
import { useCustomer } from "@/hooks/use-customer";
import { formatWeight } from "@/utils/format-weight";
import { formatCurrency } from "@/utils/formatCurrency";
import { JOURNEYS } from "@/utils/journeys";
import { IconBanner, ShopBanner } from "@arthursenno/lizenzero-ui-react/Banner";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { East, KeyboardArrowUp, Padlock } from "@arthursenno/lizenzero-ui-react/Icon";
import { useSession } from "next-auth/react";
import { useTranslations } from "next-intl";
import { enqueueSnackbar } from "notistack";
import { useState } from "react";

import { JourneyInformationCoupon } from "../journey-coupon";

export function JourneyShoppingCart() {
  const session = useSession();
  const router = useRouter();
  const t = useTranslations("shop.common.journey.shoppingCart");

  const [isRedirecting, setIsRedirecting] = useState(false);
  const [showError, setShowError] = useState(false);

  const { customer } = useCustomer();

  const { shoppingCart, deleteCartItem, isUpdatingCart } = useShoppingCart();
  const [loadingItem, setLoadingItem] = useState<number | null>(null);
  const { currentJourney } = useJourney();

  const isLongJourney = currentJourney?.type === "LONG";

  async function handleRemoveItem(cartItemId: number) {
    setLoadingItem(cartItemId);
    try {
      await deleteCartItem(cartItemId);

      enqueueSnackbar(t("removeCountryMsg"), { variant: "success" });
    } finally {
      setLoadingItem(null);
    }
  }

  function handleNext() {
    if (!shoppingCart?.items.length) return setShowError(true);

    setIsRedirecting(true);

    if (session.status === "unauthenticated" || !session.data?.user) {
      router.push("./create-account");
      return;
    }

    const user = session.data?.user;

    if (!user?.has_password && shoppingCart.journey === "LONG") {
      router.push("./set-password");
      return;
    }

    if (!customer?.hasActiveContract) {
      router.push("./informations");
      return;
    }

    router.push("./billing");
  }

  const licenses = shoppingCart.items.filter((item) => item.service_type === "EU_LICENSE");
  const actionGuides = shoppingCart.items.filter((item) => item.service_type === "ACTION_GUIDE");
  const directLicense = shoppingCart.items.find((item) => item.service_type === "DIRECT_LICENSE");

  const disabledBtnNext =
    showError || isRedirecting || isUpdatingCart || (shoppingCart?.items && !licenses.length && !actionGuides.length);

  const euLicenseReconfigurePath = isLongJourney
    ? `/${JOURNEYS["LONG"].basePath}/select-countries`
    : `/${JOURNEYS["QUICK_LICENSE"].basePath}/calculator`;
  const actionGuideReconfigurePath = `/${JOURNEYS["QUICK_ACTION_GUIDE"].basePath}/shopping-cart?select-countries=true`;
  const directLicenseReconfigurePath = `/${JOURNEYS["DIRECT_LICENSE"].basePath}/calculator`;

  return (
    <>
      <div className="p-6 md:p-10 rounded-4xl bg-surface-02">
        <p className="text-[28px] text-primary font-bold">{t("resume")}</p>
        {/* License Service */}
        {!!licenses.length && (
          <div>
            <div className="flex items-center justify-between mt-6">
              <p className="text-xl text-primary font-bold text-nowrap">{t("serviceLicense")}*</p>
              <div className="flex items-center justify-end mt-6">
                <Link href={euLicenseReconfigurePath}>
                  <Button
                    // onClick={() => handleReconfigureProduct("EU_LICENSE")}
                    color="light-blue"
                    variant="text"
                    size="small"
                    style={{ textWrap: "nowrap" }}
                  >
                    {t("reconfigure")}
                  </Button>
                </Link>
              </div>
            </div>
            <div className="flex flex-col gap-4 mt-6">
              {licenses.map((item) => (
                <div className="py-3 px-4 rounded-2xl border border-surface-03 bg-background" key={item.id}>
                  <div>
                    <div className="flex items-center justify-between">
                      <div className="py-4 flex items-center gap-3">
                        <CountryIcon
                          country={{ flag_url: item.country_flag, name: item.country_name }}
                          className="size-7"
                        />
                        <p className="text-primary text-xl font-bold">{item.country_name}</p>
                      </div>
                      <div className="md:hidden">
                        <Button
                          className="text-base font-bold"
                          onClick={() => handleRemoveItem(item.id)}
                          disabled={isUpdatingCart}
                          color="light-blue"
                          variant="text"
                          size="small"
                          trailingIcon={loadingItem === item.id && <Spinner size="xs" />}
                        >
                          {t("removeCountry")}
                        </Button>
                      </div>
                    </div>
                    <Divider initialMarginDisabled />
                  </div>
                  <div>
                    <div className="flex items-center py-4 gap-3 justify-between">
                      <p className="text-primary text-sm">{t("fees.registrationFee")}</p>
                      <p className="text-primary text-sm font-bold flex-none">
                        {formatCurrency(item.price_list?.registration_fee || 0)}
                      </p>
                    </div>
                    <hr className="text-on-surface-01 opacity-30" />
                  </div>
                  <div>
                    <div className="flex items-center py-4 gap-3 justify-between">
                      <p className="text-primary text-sm">{t("fees.handlingFee")}</p>
                      <p className="text-primary text-sm font-bold flex-none">
                        {formatCurrency(item.price_list?.handling_fee || 0)}
                      </p>
                    </div>
                    <hr className="text-on-surface-01 opacity-30" />
                  </div>
                  <div className="hidden md:flex items-center justify-end mt-1 py-4">
                    <Button
                      className="text-base font-bold"
                      onClick={() => handleRemoveItem(item.id)}
                      disabled={isUpdatingCart}
                      color="light-blue"
                      variant="text"
                      size="small"
                      trailingIcon={loadingItem === item.id && <Spinner size="xs" />}
                    >
                      {t("removeCountry")}
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
        {/* Action Guide */}
        {!!actionGuides.length && (
          <div>
            <div className="flex items-center justify-between mt-6">
              <p className="text-xl text-primary font-bold text-nowrap">{t("actionGuide")}</p>
              <div className="flex items-center justify-end mt-6">
                <Link href={actionGuideReconfigurePath}>
                  <Button color="light-blue" variant="text" size="small" style={{ textWrap: "nowrap" }}>
                    {t("reconfigure")}
                  </Button>
                </Link>
              </div>
            </div>
            <div className="flex flex-col gap-4 mt-6">
              {actionGuides.map((item) => (
                <div className="py-3 px-4 rounded-2xl border border-surface-03 bg-background" key={item.id}>
                  <div>
                    <div className="flex items-center justify-between">
                      <div className="py-4 flex items-center gap-3">
                        <CountryIcon
                          country={{ flag_url: item.country_flag, name: item.country_name }}
                          className="size-7"
                        />
                        <p className="text-primary text-xl font-bold">{item.country_name}</p>
                      </div>
                      <div className="md:hidden">
                        <Button
                          className="text-base font-bold"
                          onClick={() => handleRemoveItem(item.id)}
                          disabled={isUpdatingCart}
                          color="light-blue"
                          variant="text"
                          size="small"
                          trailingIcon={loadingItem === item.id && <Spinner size="xs" />}
                        >
                          {t("removeCountry")}
                        </Button>
                      </div>
                    </div>
                    <Divider initialMarginDisabled />
                  </div>
                  <div>
                    <div className="flex items-center py-4 gap-3 justify-between">
                      <p className="text-primary text-sm">{t("actionGuide")}</p>
                      <p className="text-primary text-sm font-bold flex-none">
                        {formatCurrency(item.price_list?.price || 0)}
                      </p>
                    </div>
                    <hr className="text-on-surface-01 opacity-30" />
                  </div>
                  <div className="hidden md:flex items-center justify-end mt-1 py-4">
                    <Button
                      className="text-base font-bold"
                      onClick={() => handleRemoveItem(item.id)}
                      disabled={isUpdatingCart}
                      color="light-blue"
                      variant="text"
                      size="small"
                      trailingIcon={loadingItem === item.id && <Spinner size="xs" />}
                    >
                      {t("removeCountry")}
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
        {/* Direct License */}
        {!!directLicense && (
          <div>
            <div className="flex items-center justify-between mt-6">
              <p className="text-xl text-primary font-bold text-nowrap">{t("directLicense")}</p>
              <div className="flex items-center justify-end mt-6">
                <Link href={directLicenseReconfigurePath}>
                  <Button color="light-blue" variant="text" size="small" style={{ textWrap: "nowrap" }}>
                    {t("reconfigure")}
                  </Button>
                </Link>
              </div>
            </div>
            <div className="flex flex-col gap-4 mt-6">
              <div className="py-3 px-4 rounded-2xl border border-surface-03 bg-background">
                <div>
                  <div className="flex items-center justify-between">
                    <div className="py-4 flex items-center gap-3">
                      <CountryIcon
                        country={{ flag_url: directLicense.country_flag, name: directLicense.country_name }}
                        className="size-7"
                      />
                      <p className="text-primary text-xl font-bold">{directLicense.country_name}</p>
                    </div>
                    <div className="md:hidden">
                      <Button
                        className="text-base font-bold"
                        onClick={() => handleRemoveItem(directLicense.id)}
                        disabled={isUpdatingCart}
                        color="light-blue"
                        variant="text"
                        size="small"
                        trailingIcon={loadingItem === directLicense.id && <Spinner size="xs" />}
                      >
                        {t("removeCountry")}
                      </Button>
                    </div>
                  </div>
                  <Divider initialMarginDisabled />
                </div>
                <div>
                  <div className="py-4">
                    <div className="flex items-center py-2 gap-3 justify-between">
                      <p className="text-primary text-sm">{t("directLicense")}</p>
                      <p className="text-primary text-sm font-bold flex-none">{formatCurrency(directLicense.price)}</p>
                    </div>
                    {directLicense.packaging_services && !!Object.values(directLicense.packaging_services).length && (
                      <div className="mt-4 space-y-3">
                        {directLicense.packaging_services.map((packagingService) => (
                          <details key={packagingService.id} className="group">
                            <summary className="flex gap-4 text-tonal-dark-cream-20 font-bold items-center">
                              {packagingService.name}
                              <KeyboardArrowUp
                                width={24}
                                height={24}
                                className="chevron group-open:rotate-180 transition-all duration-300 fill-support-blue w-7 h-7"
                              />
                            </summary>
                            {Object.entries(packagingService.fractions).map(([fractionCode, fraction]) => (
                              <div key={`${packagingService.id}_${fractionCode}`}>
                                <div className="py-4 px-3 flex justify-between items-center">
                                  <p className="text-base text-tonal-dark-cream-20">{fraction.name}</p>
                                  <p className="text-base text-tonal-dark-cream-20">{formatWeight(fraction.weight)}</p>
                                </div>

                                <Divider style={{ margin: 0 }} />
                              </div>
                            ))}
                          </details>
                        ))}
                      </div>
                    )}
                  </div>

                  <hr className="text-on-surface-01 opacity-30" />
                </div>
                <div className="hidden md:flex items-center justify-end mt-1 py-4">
                  <Button
                    className="text-base font-bold"
                    onClick={() => handleRemoveItem(directLicense.id)}
                    disabled={isUpdatingCart}
                    color="light-blue"
                    variant="text"
                    size="small"
                    trailingIcon={loadingItem === directLicense.id && <Spinner size="xs" />}
                  >
                    {t("removeCountry")}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        <Divider style={{ marginTop: "28px", marginBottom: "28px" }} />
        <JourneyInformationCoupon />
        <div className="mt-7 p-4 flex items-center justify-between rounded-b-2xl bg-[#CAE5EC]">
          <p className="text-primary font-bold text-xl">Total</p>
          <p className="text-primary font-bold text-2xl">{formatCurrency(shoppingCart?.total || 0)}</p>
        </div>
        <p className="italic text-on-surface-01 text-sm mt-6">{t("fees.annualHandlingFee")}</p>
        <div className="flex items-center justify-end w-full mt-6">
          <div className="w-full md:w-3/12 flex flex-col gap-2">
            {showError && <p className="text-error text-right block">{t("selectValidCountry")}</p>}
            <Button
              color="yellow"
              variant="filled"
              size="medium"
              trailingIcon={<East />}
              onClick={handleNext}
              style={{ width: "100%" }}
              disabled={disabledBtnNext}
            >
              {t("buttons.next")}
            </Button>
          </div>
        </div>
      </div>
      <div className="mt-10 flex flex-col md:flex-row items-start gap-6">
        <div className="w-full">
          <ShopBanner title="" style={{ width: "100%" }}>
            <IconBanner
              className="text-white "
              icon={() => <Padlock width={24} height={24} className="fill-tonal-dark-blue-80" />}
            />

            <div className="">
              <p className="font-bold text-base">{t("banner.title")}</p>
              <span className="w-full text-sm ">{t("banner.description")}</span>
            </div>
          </ShopBanner>
        </div>
        <ShopTrustpilot />
      </div>
    </>
  );
}
