"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { ReactNode, useEffect } from "react";
import { FormProvider, useForm, useWatch } from "react-hook-form";
import { z } from "zod";

import { useShoppingCart } from "@/hooks/use-shopping-cart";

const journeySelectCountriesFormSchema = z.object({
  items: z
    .record(
      z.string(),
      z.object({
        commitmentAnswers: z.record(z.string(), z.string()),
        packagingServices: z.record(
          z.string(),
          z
            .object({
              id: z.number().optional(),
              fractions: z.record(
                z.string(),
                z.object({
                  code: z.string(),
                  name: z.string(),
                  weight: z.number().gte(0, { message: "The minimum weight is 0." }).optional().default(0),
                })
              ),
            })
            .optional()
        ),
      })
    )
    .refine((items) => items && Object.keys(items).length > 0, {
      message: "Select at least one country.",
      path: ["minimum"],
    }),
});

export type JourneySelectCountriesFormData = z.infer<typeof journeySelectCountriesFormSchema>;
type FormItems = JourneySelectCountriesFormData["items"];
type FormItem = FormItems[string];

interface JourneySelectCountriesProviderProps {
  children: ReactNode;
}

export function JourneySelectCountriesProvider({ children }: JourneySelectCountriesProviderProps) {
  const { shoppingCart } = useShoppingCart();

  const methods = useForm<JourneySelectCountriesFormData>({
    resolver: zodResolver(journeySelectCountriesFormSchema),
    mode: "all",
  });

  const { control, getValues, unregister, reset } = methods;

  const formValues = useWatch({
    control: control,
  });

  useEffect(() => {
    const formItems = Object.keys(formValues.items || {});

    if (formItems.length) {
      formItems.forEach((countryCode) => {
        const cartItem = shoppingCart.items.find(
          (item) => item.service_type === "EU_LICENSE" && item.country_code === countryCode
        );

        if (!cartItem) unregister(`items.${countryCode}`);
      });

      return;
    }

    const licenses = shoppingCart.items.filter((item) => item.service_type === "EU_LICENSE");

    const entries = licenses.reduce(
      (acc, licenseItem) => {
        const customerCommitment = shoppingCart.customer_commitments.find(
          (c) => c.country_code === licenseItem.country_code
        );
        acc[licenseItem.country_code] = {
          commitmentAnswers: Object.values(customerCommitment?.commitment || {}).reduce(
            (acc, commimentQuestion) => {
              acc[`criteria_${commimentQuestion.id}`] = commimentQuestion.answer as string;
              return acc;
            },
            {} as Record<string, string>
          ),
          packagingServices: (licenseItem.packaging_services || []).reduce(
            (acc, packagingService) => {
              acc[`p_${packagingService.id}`] = {
                id: Number(packagingService.id),
                name: packagingService.name,
                fractions: packagingService.fractions,
              };
              return acc;
            },
            {} as Record<
              string,
              { id: number; name: string; fractions: Record<string, { code: string; name: string; weight: number }> }
            >
          ),
        };
        return acc;
      },
      {} as Record<string, FormItem>
    );

    reset({
      items: entries,
    });
  }, [shoppingCart.items]);

  return <FormProvider {...methods}>{children}</FormProvider>;
}
