"use client";

import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Aluminium, Padlock } from "@arthursenno/lizenzero-ui-react/Icon";

import { FractionInput } from "@/components/_common/forms/fraction-input";
import {
  QuestionTooltip,
  QuestionTooltipDescription,
  QuestionTooltipTitle,
} from "@/components/_common/question-tooltip";
import { TooltipIcon } from "@/components/_common/tooltipIcon";
import { Skeleton } from "@/components/ui/skeleton";
import { useShoppingCart } from "@/hooks/use-shopping-cart";
import { formatCurrency } from "@/utils/formatCurrency";
import { IconBanner, ShopBanner } from "@arthursenno/lizenzero-ui-react/Banner";
import { Controller, useFormContext, useWatch } from "react-hook-form";
import { GermanyCalculatorFormData } from "./germany-calculator-provider";
import { calculateDirectLicenseNetValue } from "@/utils/calculate-germany-total-amount";
import { usePricing } from "@/hooks/use-pricing";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface GermanyCalculatorProps {
  estimatorRef: any;
}

export function GermanyCalculator({ estimatorRef }: GermanyCalculatorProps) {
  const { shoppingCart, updateCartItem, isUpdatingCart } = useShoppingCart();

  const { priceLists, isLoading: isLoadingPriceLists } = usePricing({
    serviceType: "DIRECT_LICENSE",
  });

  const {
    formState: { errors },
    control,
  } = useFormContext<GermanyCalculatorFormData>();

  const directLicense = shoppingCart.items.find((item) => item.service_type === "DIRECT_LICENSE");

  const packagingService = useWatch({ control, name: "packagingService" });
  const licenseYear = useWatch({ control, name: "licenseYear" });

  const currentPriceList = priceLists?.find((priceList) => Number(priceList.condition_type_value) === licenseYear);

  const fractionList = Object.values(packagingService?.fractions || {});

  const netValue = calculateDirectLicenseNetValue({
    fractions: fractionList,
    priceList: currentPriceList!,
    currentFractions: null,
  });
  const formattedSimulationPrice = formatCurrency(netValue || 0);

  async function handleChangeLicenseYear(year: number) {
    if (!directLicense) return;

    await updateCartItem(directLicense.id, { year });
  }

  const isLoading = isUpdatingCart || isLoadingPriceLists || !directLicense;

  return (
    <form className="flex flex-col gap-6 w-full">
      <div className="bg-surface-02 rounded-4xl p-10">
        <div className="flex flex-row items-center justify-between mb-10">
          <span className="text-on-tertiary font-bold">License Year</span>
          {!isLoading && (
            <div className="flex items-center gap-2">
              <QuestionTooltip>
                <QuestionTooltipDescription className="p-1">The Direct License year</QuestionTooltipDescription>
              </QuestionTooltip>
              <Select
                defaultValue={String(directLicense.year)}
                onValueChange={(value) => handleChangeLicenseYear(Number(value))}
              >
                <SelectTrigger className="bg-transparent border-none w-auto p-1 rounded-lg text-support-blue font-bold focus:ring-support-blue">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent className="z-[1002]">
                  {priceLists?.map((priceList) => (
                    <SelectItem key={priceList.id} value={priceList.condition_type_value}>
                      {priceList.condition_type_value}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
          {isLoading && <Skeleton className="h-6 w-24" />}
        </div>
        <div className="space-y-5 mb-5">
          <div className="flex flex-row gap-4 items-center">
            <span className="text-2xl text-primary font-bold">Direct License</span>
            <TooltipIcon info="Direct license calculator" />
          </div>
          <div className="flex flex-col gap-8">
            {!isLoading && !!fractionList.length && (
              <div className="rounded-2xl overflow-hidden space-y-[1px] bg-tonal-dark-cream-80">
                {fractionList.map((fraction, fractionIndex) => (
                  <div key={fraction.code} className="bg-white">
                    <div className="py-3 px-5 flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
                      <div className="flex items-center gap-4">
                        <QuestionTooltip>
                          <QuestionTooltipTitle>
                            <Aluminium width={24} className="fill-primary" />
                            <p className="text-primary text-md font-bold">{fraction.name}</p>
                          </QuestionTooltipTitle>
                          <QuestionTooltipDescription className="text-primary">
                            Bottle tops, film for chocolate and tubes for skincare products: packaging made from
                            aluminium is mostly used to package food, cosmetics or pharmaceutical products. Enter the
                            estimated total weight of packaging that you will place on the German market in the
                            respective licence year.
                          </QuestionTooltipDescription>
                        </QuestionTooltip>
                        <div className="flex flex-1 items-center gap-3">
                          <Aluminium className="size-6 md:size-9 fill-primary" />
                          <p className="text-sm md:text-base font-bold text-primary">{fraction.name}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-4 w-full md:w-48 flex-shrink-0">
                        <Controller
                          key={`fraction_${fraction.code}`}
                          name={`packagingService.fractions.${fraction.code}.weight`}
                          control={control}
                          render={({ field }) => (
                            <FractionInput
                              {...field}
                              type="weight"
                              value={field.value}
                              data-invalid={
                                !!errors &&
                                !!errors.packagingService &&
                                !!errors.packagingService.fractions &&
                                !!errors.packagingService.fractions[fraction.code]
                              }
                              onChange={(value) => field.onChange(value)}
                            />
                          )}
                        />
                        <span className="text-base text-primary">kg</span>
                      </div>
                    </div>
                  </div>
                ))}
                <div className="bg-white py-[14px] px-5 flex flex-col-reverse md:flex-row items-end gap-4 md:gap-0 md:items-center justify-between">
                  <Button
                    onClick={(e) => {
                      e.preventDefault();
                      estimatorRef.current.scrollIntoView();
                    }}
                    size="small"
                    color="dark-blue"
                    variant="filled"
                  >
                    Need help calculating?
                  </Button>

                  <div className="flex flex-col text-right py-5 text-primary">
                    <span className="font-bold mb-2">Your net price</span>
                    <span className="text-[40px] font-bold"> {formattedSimulationPrice}</span>
                    <span className="text-on-surface-01">per year</span>
                  </div>
                </div>
              </div>
            )}
            {isLoading && (
              <div className="flex flex-col gap-8">
                <div className="rounded-2xl overflow-hidden bg-white px-5 py-3">
                  <div className="py-3">
                    <Skeleton className="h-12" />
                  </div>
                  <div className="py-3">
                    <Skeleton className="h-12" />
                  </div>
                  <div className="py-3">
                    <Skeleton className="h-12" />
                  </div>
                  <div className="py-3">
                    <Skeleton className="h-12" />
                  </div>
                  <div className="py-3">
                    <Skeleton className="h-12" />
                  </div>
                  <div className="py-3">
                    <Skeleton className="h-12" />
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
        <ShopBanner title="" style={{ width: "100%", padding: "32px" }} className="bg-[#D8F2D8]">
          <IconBanner
            className="text-white"
            icon={() => <Padlock width={24} height={24} className="fill-tonal-dark-blue-80" />}
          />
          <div className="">
            <p className="font-bold text-base">You can't go wrong.</p>
            <span className="w-full text-sm">
              Quantities can be flexibly adjusted at any time via your customer account, even after the contract has
              been concluded!
            </span>
          </div>
        </ShopBanner>
      </div>
    </form>
  );
}
