import { cn } from "@/lib/utils";
import { PackagingMaterialTabProps, PackagingSizeTabProps } from "./types";

const PackagingEstimationMaterialTab = ({ isSelected, icon: Icon, label, onClick }: PackagingMaterialTabProps) => {
  const baseStyle = "bg-white rounded-t-4xl py-4 col-span-1 text-primary cursor-pointer font-bold flex justify-center";
  const selectedStyle = "bg-primary text-white";

  return (
    <button onClick={onClick} className={cn(baseStyle, isSelected && selectedStyle)}>
      <div className="mx-auto w-fit flex flex-row items-center gap-2">
        <Icon />
        <span className="mt-1">{label}</span>
      </div>
    </button>
  );
};

const PackagingEstimationSizeTab = ({ isSelected, label, onClick }: PackagingSizeTabProps) => {
  const baseStyle = "bg-surface-02 rounded-[16px] py-2 col-span-1 text-primary cursor-pointer font-bold px-4";
  const selectedStyle = "bg-primary text-white";

  return (
    <button onClick={onClick} className={cn(baseStyle, isSelected && selectedStyle)}>
      {label}
    </button>
  );
};

export { PackagingEstimationMaterialTab, PackagingEstimationSizeTab };
