"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { ReactNode, useEffect } from "react";
import { FormProvider, useForm, useWatch } from "react-hook-form";
import { z } from "zod";

import { useShoppingCart } from "@/hooks/use-shopping-cart";

const germanyCalculatorFormSchema = z.object({
  licenseYear: z.number(),
  packagingService: z.object({
    id: z.number(),
    name: z.string(),
    fractions: z.record(
      z.string(),
      z.object({
        code: z.string(),
        name: z.string(),
        weight: z.number().gte(0, { message: "The minimum weight is 0." }).optional().default(0),
        price: z.number(),
      })
    ),
  }),
});

export type GermanyCalculatorFormData = z.infer<typeof germanyCalculatorFormSchema>;

interface GermanyCalculatorProviderProps {
  children: ReactNode;
}

export function GermanyCalculatorProvider({ children }: GermanyCalculatorProviderProps) {
  const { shoppingCart, addCartItem } = useShoppingCart();

  const methods = useForm<GermanyCalculatorFormData>({
    resolver: zodResolver(germanyCalculatorFormSchema),
  });

  const { control, reset } = methods;

  const packagingService = useWatch({
    control,
    name: "packagingService",
  });

  const directLicense = shoppingCart.items.find((item) => item.service_type === "DIRECT_LICENSE");

  useEffect(() => {
    if (!directLicense || !directLicense.packaging_services) {
      addCartItem("DE", "DIRECT_LICENSE");
      return;
    }

    const priceList = directLicense.price_list;

    const firstThreshold = priceList.thresholds && priceList.thresholds[0];

    if (!firstThreshold) return;

    const directLicensePackagingService = directLicense.packaging_services[0];

    const data: GermanyCalculatorFormData = {
      licenseYear: directLicense.year,
      packagingService: {
        id: directLicense.packaging_services[0].id,
        name: directLicense.packaging_services[0].name,
        fractions: Object.values(firstThreshold.fractions).reduce(
          (acc, fraction) => {
            acc[fraction.code] = {
              code: fraction.code,
              name: fraction.name,
              weight:
                packagingService?.fractions[fraction.code]?.weight ||
                directLicensePackagingService?.fractions[fraction.code]?.weight ||
                0,
              price: fraction.value,
            };
            return acc;
          },
          {} as Record<string, { code: string; name: string; weight: number; price: number }>
        ),
      },
    };

    reset(data);
  }, [directLicense?.year]);

  return <FormProvider {...methods}>{children}</FormProvider>;
}
