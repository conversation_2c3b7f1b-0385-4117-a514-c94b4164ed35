import { TooltipIcon } from "@/components/_common/tooltipIcon";
import { Divider } from "@/components/_common/divider";
import { Icons } from "@/components/ui/icons";
import { GLASS_RESOURCES_FACTOR, PAPER_RESOURCES_FACTOR, PLASTICS_RESOURCES_FACTOR } from "@/utils/system-consts";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { useEffect, useState } from "react";
import { options } from "./options";
import { PackagingEstimationMaterialTab, PackagingEstimationSizeTab } from "./packaging-estimator-tabs";
import { PackagingMaterial, PackagingSize } from "./types";
import { useMediaQuery } from "@/hooks/use-media-query";
import { useTranslations } from "next-intl";

export default function PackagingEstimator() {
  const t = useTranslations("shop.directLicense.calculator.estimator");
  const [selectedMaterial, setSelectedMaterial] = useState<PackagingMaterial>("paper");
  const [selectedSize, setSelectedSize] = useState<PackagingSize>("small");
  const [paperTotal, setPaperTotal] = useState<number>(0);
  const [plasticsTotal, setPlasticsTotal] = useState<number>(0);
  const [glassTotal, setGlassTotal] = useState<number>(0);
  const [resourcesSaved, setResourcesSaved] = useState<number>(0);

  const [formValues, setFormValues] = useState<{ [key: string]: number }>({});

  const isMobile = useMediaQuery("(max-width: 768px)");

  const handleInputChange = (id: number, value: number) => {
    if (value < 0) return;
    setFormValues((prevValues) => ({
      ...prevValues,
      [id]: value,
    }));
  };

  const calculateTotals = () => {
    let newPaperTotal = 0;
    let newPlasticsTotal = 0;
    let newGlassTotal = 0;

    options.forEach((form) => {
      const value = formValues[form.id] || 0;
      if (form.multipliers.paper && form.group === "paper") newPaperTotal += value * (form.multipliers.paper / 1000);
      if (form.multipliers.plastic && form.group === "plastics")
        newPlasticsTotal += value * (form.multipliers.plastic / 1000);
      if (form.multipliers.glass && form.group === "glass") newGlassTotal += value * (form.multipliers.glass / 1000);
    });

    setPaperTotal(newPaperTotal);
    setPlasticsTotal(newPlasticsTotal);
    setGlassTotal(newGlassTotal);
  };

  const calculateSavings = () => {
    const paperSavings = paperTotal * PAPER_RESOURCES_FACTOR;
    const plasticsSavings = plasticsTotal * PLASTICS_RESOURCES_FACTOR;
    const glassSavings = glassTotal * GLASS_RESOURCES_FACTOR;

    const totalSavings = paperSavings + plasticsSavings + glassSavings;
    setResourcesSaved(totalSavings);
  };

  useEffect(() => {
    calculateTotals();
  }, [formValues]);

  useEffect(() => {
    calculateSavings();
  }, [paperTotal, plasticsTotal, glassTotal]);

  const renderForms = () => {
    return (
      <div>
        {options
          .filter((form) => form.group === selectedMaterial && form.size === selectedSize)
          .map((form) => (
            <div key={form.id}>
              <div className={`grid ${isMobile ? "grid-cols-2" : "grid-cols-3"} gap-4 text-primary`}>
                <div className="flex flex-col">
                  <span className="font-bold">{form.label}</span>
                  {isMobile && <span className="text-sm text-gray-600">{form.description}</span>}
                </div>

                {!isMobile && <span className="text-sm text-gray-600">{form.description}</span>}
                <div className="flex flex-row gap-2 items-center">
                  <Input
                    type="number"
                    value={formValues[form.id] || ""}
                    placeholder="0"
                    onChange={(e: any) => handleInputChange(form.id, Number(e.target.value))}
                  />
                  <span>Pcs.</span>
                </div>
              </div>
              <Divider key={`${form.id}-divider`} style={{ marginBottom: 10, marginTop: 15 }} />
            </div>
          ))}
      </div>
    );
  };

  const MaterialIcon = ({ selectedMaterial }: { selectedMaterial: PackagingMaterial }) => {
    switch (selectedMaterial) {
      case "paper":
        return <Icons.cardboard />;
      case "plastics":
        return <Icons.plastics />;
      case "glass":
        return <Icons.glass />;
      default:
        return <Icons.glass />;
    }
  };

  return (
    <div className="flex flex-col gap-1">
      {!isMobile ? (
        <div className={`grid grid-cols-3 gap-1`}>
          <PackagingEstimationMaterialTab
            onClick={() => setSelectedMaterial("paper")}
            label={t("tabs.material.paper")}
            icon={Icons.cardboard}
            isSelected={selectedMaterial === "paper"}
          />
          <PackagingEstimationMaterialTab
            onClick={() => setSelectedMaterial("plastics")}
            label={t("tabs.material.plastics")}
            icon={Icons.plastics}
            isSelected={selectedMaterial === "plastics"}
          />
          <PackagingEstimationMaterialTab
            onClick={() => setSelectedMaterial("glass")}
            label={t("tabs.material.glass")}
            icon={Icons.glass}
            isSelected={selectedMaterial === "glass"}
          />
        </div>
      ) : (
        <div className="bg-primary rounded-t-4xl py-4 col-span-1 cursor-pointer font-bold flex justify-center">
          <div className="mx-auto w-fit flex flex-row items-center gap-2">
            <MaterialIcon selectedMaterial={selectedMaterial} />
            <select
              onChange={(e) => setSelectedMaterial(e.target.value as PackagingMaterial)}
              className="mt-1 bg-primary"
            >
              <option value="paper">Paper and Cardboard</option>
              <option value="plastics">Plastics</option>
              <option value="glass">Glass</option>
            </select>
          </div>
        </div>
      )}

      <div className="bg-white rounded-b-4xl p-10">
        <div className="flex flex-col md:flex-row justify-between gap-4 mb-10">
          <div className="grid grid-cols-3 gap-4">
            <PackagingEstimationSizeTab
              onClick={() => setSelectedSize("small")}
              isSelected={selectedSize === "small"}
              label={t("tabs.size.small")}
            />
            <PackagingEstimationSizeTab
              onClick={() => setSelectedSize("medium")}
              isSelected={selectedSize === "medium"}
              label={t("tabs.size.medium")}
            />
            <PackagingEstimationSizeTab
              onClick={() => setSelectedSize("large")}
              isSelected={selectedSize === "large"}
              label={t("tabs.size.large")}
            />
          </div>
          <div className="flex flex-row justify-between gap-4 items-center px-4 bg-[#D8F2D8] rounded-[100px] h-[56px]">
            <Icons.leaf className="fill-[#1B6C64]" />
            <div className="space-x-2">
              <span className="text-[#1B6C64] font-bold">{t("resourcesSaved")}:</span>
              <span className="text-primary font-bold">{resourcesSaved.toFixed(3)} kg</span>
            </div>
            <TooltipIcon />
          </div>
        </div>

        <div className="mb-8">{renderForms()}</div>

        <div className="flex flex-col md:flex-row gap-6 justify-between">
          <div className="flex flex-col gap-4">
            <span className="text-primary text-xl">{t("tabs.material.paper")}</span>
            <span className="text-primary font-bold text-3xl">{paperTotal.toFixed(3)} kg</span>
          </div>

          <div className="flex flex-col gap-4">
            <span className="text-primary text-xl">{t("tabs.material.plastics")}</span>
            <span className="text-primary font-bold text-3xl">{plasticsTotal.toFixed(3)} kg</span>
          </div>

          <div className="flex flex-col gap-4">
            <span className="text-primary text-xl">{t("tabs.material.glass")}</span>
            <span className="text-primary font-bold text-3xl">{glassTotal.toFixed(3)} kg</span>
          </div>

          <div className="my-auto">
            <Button
              className="w-full"
              onClick={() => window.scrollTo(0, 300)}
              size="small"
              color="dark-blue"
              variant="filled"
            >
              {t("backToCalculator")}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
