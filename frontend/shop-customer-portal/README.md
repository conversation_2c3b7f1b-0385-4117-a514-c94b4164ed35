# README for Lizenzero SHOP and SAAS Storefront

## Introduction

Welcome to the Lizenzero SHOP and SAAS Storefront project. This project is a modern storefront built using Next.js.

## Key Features

- **Next.js Framework**: Utilizing the latest features of Next.js for server-side rendering, static site generation, and API routes.
- **Internationalization (i18n)**: Supporting multiple languages and locales for a global audience.
- **Responsive Design**: Fully responsive layouts with Tailwind CSS.

## Development Setup

Before you start, ensure you have Node.js installed on your system. After cloning the repository, install the dependencies:

```bash
pnpm install
```

## Development Dependencies

Our project uses several development dependencies to maintain code quality and facilitate the development process:

- **TypeScript**: Strongly typed programming language that builds on JavaScript.
- **ESLint & Prettier**: Ensuring code quality and consistency.
- **Husky**: For managing Git hooks.
- **Tailwind CSS**: A utility-first CSS framework for rapidly building custom designs.
- **PostCSS**: A tool for transforming CSS with JavaScript.
- **Various ESLint Plugins**: Enhancing ESLint capabilities with plugins for React, import, promise, and more.
- **@typescript-eslint**: TypeScript parser and ESLint plugin.
- **CommitLint**: Enforcing commit message conventions.

See `package.json` for a complete list of dependencies.

## i18n Configuration

We use robust internationalization frameworks to manage multiple languages. This allows for content to be easily adapted for different locales.

## Design

For UI/UX design, please refer to our Figma link: [Figma Design](#)
