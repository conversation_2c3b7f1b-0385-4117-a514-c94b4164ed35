ARG NODE_VERSION=20.18.1
ARG PROJECT_NAME
ARG COMPONENT
ARG BRANCH

FROM ************.dkr.ecr.eu-central-1.amazonaws.com/${PROJECT_NAME}-${COMPONENT}-build:${CI_COMMIT_REF_SLUG} AS builder
FROM gitlab.interzero.de/software-development/dependency_proxy/containers/node:${NODE_VERSION}-alpine AS runner

WORKDIR /app

ENV NEXT_TELEMETRY_DISABLED 1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000
ENV PORT 3000
ENV HOSTNAME 0.0.0.0
ENV NODE_ENV=${NODE_ENV}
CMD ["node", "server.js"]
