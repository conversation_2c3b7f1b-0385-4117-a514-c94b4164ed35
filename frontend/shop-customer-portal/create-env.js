/* eslint-disable @typescript-eslint/no-var-requires */
const fs = require("fs");
const path = require("path");

// 1. Read the .env.sample file
const envSamplePath = path.resolve(__dirname, ".env.sample");
const envSample = fs.readFileSync(envSamplePath, "utf-8");

console.warn(`🫢 Creating .env file...`);

// 2. Replace placeholders with actual environment variables
const envContent = envSample.replace(/\${(\w+)}/g, (_, envVar) => {
  const value = process.env[envVar];
  if (!value) {
    console.warn(`❌ Environment variable ${envVar} is not set.`);

    return "";
  }

  console.warn(`✅ Environment variable ${envVar} is set.`);

  return value;
});

// 3. Write the result to a .env file
const envPath = path.resolve(__dirname, ".env");
fs.writeFileSync(envPath, envContent);

console.log("🫢✅ .env file created successfully!");
