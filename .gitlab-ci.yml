# ----------------- GLOBAL CONFIGURATIONS ----------------- #
default:
  tags: ["aws"]
  cache:
    key: ${CI_PROJECT_NAME}-pnpm-store
    paths:
      - .pnpm-store

image: gitlab.interzero.de/software-development/dependency_proxy/containers/node:${NODE_VERSION}-alpine

include:
  - project: "devops-templates/ci-templates"
    ref: main
    file: "/teams/notifications.yml"
  - template: Jobs/SAST.gitlab-ci.yml
  - template: Jobs/Secret-Detection.gitlab-ci.yml
  - local: frontend/admin-portal/.gitlab-ci.yml
  - local: frontend/clerk-portal/.gitlab-ci.yml
  - local: frontend/shop-customer-portal/.gitlab-ci.yml
  - local: backend/.gitlab-ci.yml

stages:
  - setup
  - install_dependencies
  - test
  - build_docker_image
  - deploy
  - teams_notifications
  - cleanup

# Global variables
variables:
  SHORT_PROJECT_NAME: oneepr
  PROJECT_NAME: oneepr
  COMPONENT: be
  NEXUS_PROD: 6000
  NEXUS_ENTW_QAT: 5000
  BRANCH: ${CI_COMMIT_REF_NAME}

before_script:
  - export BRANCH_TAG=$(echo ${CI_COMMIT_REF_NAME#*/} | tr '_' '-' | sed 's/[^a-zA-Z0-9-]//g')
  # Generate ECR token for Docker operations using custom environment variables
  - |
    if command -v aws >/dev/null 2>&1 && [ -n "$ECR_AWS_ACCESS_KEY_ID" ] && [ -n "$ECR_AWS_SECRET_ACCESS_KEY" ]; then
      export AWS_ACCESS_KEY_ID=$ECR_AWS_ACCESS_KEY_ID
      export AWS_SECRET_ACCESS_KEY=$ECR_AWS_SECRET_ACCESS_KEY
      export ECR_TOKEN=$(aws ecr get-login-password --region eu-central-1)
      aws ecr get-login-password --region eu-central-1 | docker login --username AWS --password-stdin 811261252380.dkr.ecr.eu-central-1.amazonaws.com
    else
      echo "Skipping ECR login - AWS CLI not available or ECR credentials not set"
    fi

# ----------------- SETUP ----------------- #
generate_ecr_token:
  stage: setup
  image: amazon/aws-cli:latest
  tags: ["eks-entw-qat"]
  variables:
    AWS_ACCESS_KEY_ID: $ECR_AWS_ACCESS_KEY_ID
    AWS_SECRET_ACCESS_KEY: $ECR_AWS_SECRET_ACCESS_KEY
    AWS_DEFAULT_REGION: eu-central-1
  before_script: []  # Override global before_script to avoid Docker dependency
  script:
    - echo "Generating ECR token..."
    - ECR_TOKEN=$(aws ecr get-login-password --region eu-central-1)
    - echo "ECR_TOKEN=$ECR_TOKEN" > ecr_token.env
    - echo "ECR token generated successfully (length ${#ECR_TOKEN})"
  artifacts:
    reports:
      dotenv: ecr_token.env
    expire_in: 1 hour
  only:
    - develop
    - staging
    - main
    - merge_requests
    - /^feature\/.*$/
    - /^release\/.*$/

# ----------------- SONAR ----------------- #
be_sonarqube-check:
  stage: test
  image: 811261252380.dkr.ecr.eu-central-1.amazonaws.com/maven:3.9.5-openjdk-21
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"
    GIT_DEPTH: "0"
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  script:
    - cd backend
    - mvn verify sonar:sonar
  allow_failure: true
  tags: [ "eks-entw-qat-with-s3" ]
  only:
    - merge_requests

fe_sonarqube-check:
  stage: test
  image:
    name: gitlab.interzero.de/software-development/dependency_proxy/containers/sonarsource/sonar-scanner-cli:5.0
    entrypoint: [""]
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"
    GIT_DEPTH: "0"
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  script:
    - sonar-scanner -Dsonar.projectBaseDir=./frontend
  allow_failure: true
  tags: ["eks-entw-qat-with-s3"]
  only:
    - merge_requests

# ----------------- NOTIFY ----------------- #
teams_notifications_success:
  stage: teams_notifications
  when: on_success
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"'
    - if: '$CI_COMMIT_BRANCH == "staging"'
    - if: '$CI_COMMIT_BRANCH == "main"'
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
    - if: '$CI_COMMIT_BRANCH =~ /^feature\/.*$/'
    - if: '$CI_COMMIT_BRANCH =~ /^release\/.*$/'
  before_script:
    - |
      if [ -n "$CI_ENVIRONMENT_URL" ]; then
        ICON="✅ $CI_ENVIRONMENT_URL"
      else
        ICON="✅"
      fi
  variables:
    STATUS: "success"
    COMPONENT: "MonoRepo"
  extends:
    - .pipeline_succeed_aws

teams_notifications_failed:
  stage: teams_notifications
  when: on_failure
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"'
    - if: '$CI_COMMIT_BRANCH == "staging"'
    - if: '$CI_COMMIT_BRANCH == "main"'
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
    - if: '$CI_COMMIT_BRANCH =~ /^feature\/.*$/'
    - if: '$CI_COMMIT_BRANCH =~ /^release\/.*$/'
  before_script:
    - |
      if [ -n "$CI_ENVIRONMENT_URL" ]; then
        ICON="❌ $CI_ENVIRONMENT_URL"
      else
        ICON="❌"
      fi
  variables:
    STATUS: "failed"
    COMPONENT: "MonoRepo"
  extends:
    - .pipeline_failed_aws
